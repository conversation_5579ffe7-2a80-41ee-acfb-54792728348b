﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("Batch")]
    public class Batch
    {

        [Key]
        public int IDBatch { get; set; }

        public int? IDProduct { get; set; }

        [Required]
        [StringLength(10)]
        public string ProductCodeType { get; set; }

        [Required]
        [StringLength(14)]
        public string ProductCode { get; set; }

        [Required]
        [StringLength(20)]
        public string BatchID { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? ExpiryDate { get; set; }

        public int PackSize { get; set; }

        [StringLength(10)]
        public string OriginalProductCodeType { get; set; }

        [StringLength(14)]
        public string OriginalProductCode { get; set; }

        [StringLength(20)]
        public string OriginalProductBatchID { get; set; }

        [StringLength(50)]
        public string MahID { get; set; }

        [StringLength(100)]
        public string MahName { get; set; }

        [StringLength(255)]
        public string MahStreet { get; set; }

        [StringLength(255)]
        public string MahStreet2 { get; set; }

        [StringLength(100)]
        public string MahCity { get; set; }

        [StringLength(50)]
        public string MahPostCode { get; set; }

        public string MahCountryCode { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        public bool Locked { get; set; }

        public bool IsExpiryDateZeroDay { get; set; }

        public bool SupportRequest { get; set; }

        public bool MaintenanceMode { get; set; }
    }
}
