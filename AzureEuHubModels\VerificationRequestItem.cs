﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("VerificationRequestItem")]
    public class VerificationRequestItem
    {
        [Key]
        public int IDVerificationRequestItem { get; set; }

        public int IDVerificationRequest { get; set; }

        [Required]
        public string Serial { get; set; }

        [StringLength(50)]
        public string State { get; set; }


        [InverseProperty("VerificationRequestItems")]
        public VerificationRequest VerificationRequest { get; set; }
    }
}
