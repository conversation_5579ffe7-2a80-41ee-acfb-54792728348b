﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("AlternateProduct")]
    public class AlternateProduct
    {
        [Key]
        public int IDAlternateProduct { get; set; }

        public int IDProduct { get; set; }

        [Required]
        [StringLength(10)]
        public string CodeType { get; set; }

        [Required]
        [CodeValidation(ErrorMessage = "Code is not valid GTIN or PPN number")]
        [StringLength(14)]
        public string Code { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

    }
    public class CodeValidationAttribute : ValidationAttribute
    {
        public override bool IsValid(object value)
        {
            var code = (string)value;

            try
            {
                if (Utils.GTIN.Check(code))
                    return true;
                else if (Utils.PPN.Check(code))
                    return true;
                else
                    return false;
            }
            catch (Exception e)
            {
                var debug = e.Message;
                return false;
            }

        }
    }

    public class CodeValidationAllowEmptyAttribute : ValidationAttribute
    {
        public override bool IsValid(object value)
        {
            if(value == null)
                return true;

            var code = (string)value;
            if(string.IsNullOrEmpty(code))  
                return true;

            try
            {
                if (Utils.GTIN.Check(code))
                    return true;
                else if (Utils.PPN.Check(code))
                    return true;
                else
                    return false;
            }
            catch (Exception e)
            {
                var debug = e.Message;
                return false;
            }

        }
    }
}
