﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2B099507-636B-499F-8E75-4DB08E8F79E9}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AzureEuHubModelsStandart</RootNamespace>
    <AssemblyName>AzureEuHubModelsStandart</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AddSerialResult.cs" />
    <Compile Include="Alert.cs" />
    <Compile Include="AlertApi.cs" />
    <Compile Include="AlertItem.cs" />
    <Compile Include="AlternateProduct.cs" />
    <Compile Include="AppLog.cs" />
    <Compile Include="AppLogItem.cs" />
    <Compile Include="AuditTrail.cs" />
    <Compile Include="Batch.cs" />
    <Compile Include="BatchProduct.cs" />
    <Compile Include="BatchRecallMarket.cs" />
    <Compile Include="BatchRecallResult.cs" />
    <Compile Include="BatchTargetMarket.cs" />
    <Compile Include="CallbackResult.cs" />
    <Compile Include="Country.cs" />
    <Compile Include="DBModel.cs" />
    <Compile Include="EmvoCertificate.cs" />
    <Compile Include="Event.cs" />
    <Compile Include="EventType.cs" />
    <Compile Include="HubPackStatus.cs" />
    <Compile Include="HubReport.cs" />
    <Compile Include="LogSecuritySessionToken.cs" />
    <Compile Include="Mah.cs" />
    <Compile Include="MahType.cs" />
    <Compile Include="Market.cs" />
    <Compile Include="PackError.cs" />
    <Compile Include="PackListError.cs" />
    <Compile Include="PackListErrorDescription.cs" />
    <Compile Include="PackState.cs" />
    <Compile Include="PackStatusChangeRule.cs" />
    <Compile Include="PackVerification.cs" />
    <Compile Include="PackVerificationError.cs" />
    <Compile Include="PackVerificationList.cs" />
    <Compile Include="PackVerificationStatus.cs" />
    <Compile Include="ProductError.cs" />
    <Compile Include="ProductExtend.cs" />
    <Compile Include="ProductHubEvent.cs" />
    <Compile Include="ProductStatus.cs" />
    <Compile Include="ProductTargetMarket.cs" />
    <Compile Include="ProductWithdrawalBatch.cs" />
    <Compile Include="ProductWithdrawalMarket.cs" />
    <Compile Include="ProductWithdrawalResult.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RecallMarketResult.cs" />
    <Compile Include="ResponseStatus.cs" />
    <Compile Include="SecuritySessionToken.cs" />
    <Compile Include="Serial.cs" />
    <Compile Include="SerialState.cs" />
    <Compile Include="Setting.cs" />
    <Compile Include="TargetMarket.cs" />
    <Compile Include="VerificationRequest.cs" />
    <Compile Include="VerificationRequestItem.cs" />
    <Compile Include="Wholesaler.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
</Project>