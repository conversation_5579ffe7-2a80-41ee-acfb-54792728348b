﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("PackStatusChangeRule")]
    public class PackStatusChangeRule
    {
        [Key]
        public short IDSourceStatus { get; set; }

        public short IDTargetStatus { get; set; }

        public short IDCustomerType { get; set; }

        public bool OneSerialOnly { get; set; }

        [InverseProperty("PackStatusChangeRuleSourceStatus")]
        public PackState PackStatusSourceStatus { get; set; }

        [InverseProperty("PackStatusChangeRuleTargetStatus")]
        public PackState PackStatusTargetStatus { get; set; }

    }
}
