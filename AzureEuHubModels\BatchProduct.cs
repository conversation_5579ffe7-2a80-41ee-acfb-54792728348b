﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{

    [Table("BatchProduct")]
    public class BatchProduct
    {

        [Key]
        public int IDBatchProduct { get; set; }

        [Required]
        public int IDBatch { get; set; }

        [Required]
        [StringLength(10)]
        public string CodeType { get; set; }

        [Required]
        [StringLength(14)]
        public string Code { get; set; }

        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        [StringLength(255)]
        public string CommonName { get; set; }

        [StringLength(255)]
        public string Form { get; set; }

        [StringLength(50)]
        public string PackType { get; set; }

        public int? PackSize { get; set; }

        [StringLength(100)]
        public string Strength { get; set; }

        [StringLength(50)]
        public string InternalCodeType { get; set; }

        [StringLength(50)]
        public string InternalCode { get; set; }

        public bool IsActive { get; set; }

        [StringLength(50)]
        public string SerialNumberSourceType { get; set; }

        [StringLength(128)]
        public string SerializationType { get; set; }

        public int? Version { get; set; }

        public bool IsExpiryDateZeroDay { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy}", ApplyFormatInEditMode = true)]
        public DateTime? EffectiveDate { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("BatchProduct")]
        public Batch Batch { get; set; }

    }
}
