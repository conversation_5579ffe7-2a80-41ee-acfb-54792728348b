﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("ProductError")]
    public partial class ProductError
    {
        [Key]
        public long IDProductError { get; set; }

        public int IDProductStatus { get; set; }

        [StringLength(100)]
        public string CorrelationID { get; set; }

        [StringLength(10)]
        public string Source { get; set; }

        [StringLength(2)]
        public string CountryCode { get; set; }

        [StringLength(10)]
        public string Code { get; set; }

        public string Description { get; set; }

        [InverseProperty("ProductErrors")]
        public ProductStatus ProductStatus { get; set; }
    }
}
