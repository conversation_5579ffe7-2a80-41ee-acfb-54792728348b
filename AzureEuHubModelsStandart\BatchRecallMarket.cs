﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("BatchRecallMarket")]
    public class BatchRecallMarket
    {
        [Key]
        public int IDBatchRecallMarket { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime Timestamp { get; set; }

        [StringLength(2)]
        public string Market { get; set; }

        public bool Recalled { get; set; }
    }
}
