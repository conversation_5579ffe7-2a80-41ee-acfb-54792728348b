﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("SecuritySessionToken")]
    public partial class SecuritySessionToken
    {
        [Key]
        public int IDSecuritySessionToken { get; set; }

        [Required]
        [StringLength(4000)]
        public string NewToken { get; set; }

        public int DurationHour { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        public DateTime? TimestampUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        public Guid? ReferenceID { get; set; }

    }
}
