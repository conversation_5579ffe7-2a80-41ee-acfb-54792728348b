﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("TemplateScript", Schema = "dbo")]
    public class TemplateScript
    {
        [Key]
        public int IDTemplate { get; set; }
        public string TemplateName { get; set; }
        public string Script { get; set; }
        public int IDService { get; set; }

        [Required]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [MaxLength(128)]
        public string UserCreated { get; set; }
    }
}
