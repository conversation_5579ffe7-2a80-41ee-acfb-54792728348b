﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{

    [Table("Market")]
    public class Market
    {
        [Key]
        public int IDMarket { get; set; }

        [Required]
        public int IDEvent { get; set; }

        [Required]
        public string Code { get; set; }

        public bool Recalled { get; set; }

        [InverseProperty("Markets")]
        public Event Event { get; set; }

        [InverseProperty("Markets")]
        public TargetMarket TargetMarket { get; set; }

    }
}
