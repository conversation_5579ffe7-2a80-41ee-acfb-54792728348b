﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("ProductStatus")]
    public class ProductStatus
    {
        public ProductStatus()
        {
            ProductErrors = new HashSet<ProductError>();
        }

        [Key]
        public int IDProductStatus { get; set; }

        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? Timestamp { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

 
        [InverseProperty("ProductStatus")]
        public ICollection<ProductError> ProductErrors { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((ProductStatus)obj).IDProductStatus == this.IDProductStatus)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }
    }
}
