﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("ProductWithdrawalResult")]
    public class ProductWithdrawalResult
    {
        [Key]
        public int IDProductWithdrawalResult { get; set; }

        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? Timestamp { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        [StringLength(50)]
        public string SourceType { get; set; }

        [StringLength(50)]
        public string SourceCode { get; set; }

        [StringLength(50)]
        public string AffectedBatches { get; set; }

        [StringLength(50)]
        public string AffectedPacks { get; set; }

        [StringLength(50)]
        public string ErrorCode { get; set; }

        public string ErrorDescription { get; set; }
    }
}
