﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("PackError")]
    public class PackError
    {
        public PackError()
        {
            PackListErrors = new HashSet<PackListError>();
        }

        [Key]
        public long IDPackError { get; set; }

        [Required]
        public long IDPackStatus { get; set; }

        [StringLength(10)]
        public string Code { get; set; }

        [StringLength(50)]
        public string Source { get; set; }

        [StringLength(10)]
        public string CountryCode { get; set; }

        public string Description { get; set; }

        [StringLength(10)]
        public string ProductCodeType { get; set; }

        [StringLength(50)]
        public string ProductCode { get; set; }

        [InverseProperty("PackErrors")]
        public HubPackStatus PackStatus { get; set; }

        [InverseProperty("PackError")]
        public ICollection<PackListError> PackListErrors { get; set; }

    }
}
