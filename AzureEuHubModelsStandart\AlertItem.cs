﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("AlertItem")]
    public class AlertItem
    {
        [Key]
        public int IDAlertItem { get; set; }

        public int IDAlert { get; set; }

        public string Key { get; set; }

        public string Value { get; set; }

    }
}
