﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("RecallMarketResult")]
    public class RecallMarketResult
    {
        [Key]
        public long IDRecallMarketResult { get; set; }

        public int IDEvent { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        public string Message { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime Timestamp { get; set; }

        [StringLength(128)]
        public string User { get; set; }

    }
}
