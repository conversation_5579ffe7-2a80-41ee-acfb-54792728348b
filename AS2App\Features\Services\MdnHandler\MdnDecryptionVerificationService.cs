using AS2App.Features.Services.MicHandler;
using AS2App.Models.ConfigModels;
using AS2Test.Models;
using AS2Test_master.Features.Services.Certificates;
using Microsoft.Extensions.Options;
using MimeKit;
using MimeKit.Cryptography;
using Org.BouncyCastle.Cms;
using Org.BouncyCastle.X509;
using System.Net.Http.Headers;
using System.Security.Cryptography.X509Certificates;
using System.Text;

namespace AS2Test.Features.Services.MdnHandler
{
    public class MdnDecryptionVerificationService : IMdnDecryptionVerificationService
    {
        private readonly IMicHandlerService _micHandler;
        public AzureAs2ProxyModels.DBModel _azureAs2ProxyDBModel;


        public MdnDecryptionVerificationService(ICertificateService certificateService, IConfiguration config, IMicHandlerService micHandlerService)
        {
            _micHandler = micHandlerService ?? throw new ArgumentNullException(nameof(micHandlerService));
        }

        public async Task<MdnVerificationResult> DecryptAndVerify(HttpResponseMessage mdnResponseMessage, string receiverCertificate, string senderCertificate)
        {
            var mdnBody = await mdnResponseMessage.Content.ReadAsStringAsync();
            mdnResponseMessage.Content.Headers.TryGetValues("Content-Type", out var contentTypeValues);
            var contentType = contentTypeValues?.FirstOrDefault();

            var headerBuilder = new StringBuilder();
            headerBuilder.AppendLine("MIME-Version: 1.0");
            headerBuilder.AppendLine($"Content-Type: {contentType}");
            headerBuilder.AppendLine(); // blank line to separate headers from body

            byte[] headerBytes = Encoding.ASCII.GetBytes(headerBuilder.ToString());
            byte[] bodyBytes = Encoding.ASCII.GetBytes(mdnBody);

            // Combine headers and body into a single stream
            using var combined = new MemoryStream();
            await combined.WriteAsync(headerBytes, 0, headerBytes.Length);
            await combined.WriteAsync(bodyBytes, 0, bodyBytes.Length);

            combined.Position = 0;

            var mdn = MimeEntity.Load(combined);
            var result = new MdnVerificationResult();
            try
            {
                var partnerCert = ParseCertificate(receiverCertificate);

                switch (mdn)
                {
                    case MultipartSigned multipartSigned:
                        result.MdnType = MdnType.MultipartSigned;
                        result.IsDecrypted = true;
                        HandleMultipartSigned(result, multipartSigned, partnerCert);
                        break;

                    case TextPart textPart:
                        result.MdnType = MdnType.Plain;
                        result.MdnText = textPart.Text;
                        ParseMdnContent(result);
                        break;

                    case ApplicationPkcs7Mime pkcs7 when pkcs7.SecureMimeType == SecureMimeType.SignedData:
                        result.MdnType = MdnType.ApplicationPkcs7MimeSigned;
                        HandleSignedPkcs7(result, pkcs7);
                        break;

                    case ApplicationPkcs7Mime pkcs7 when pkcs7.SecureMimeType == SecureMimeType.EnvelopedData:
                        result.MdnType = MdnType.ApplicationPkcs7MimeEnveloped;
                        HandleEncryptedPkcs7(result, pkcs7);
                        break;

                    default:
                        throw new Exception("Unsupported MDN format or type");
                }
            }
            catch (OperationCanceledException ex)
            {
                throw new InvalidOperationException("Decryption operation timed out", ex);
            }
            catch (PrivateKeyNotFoundException ex)
            {
                throw new InvalidOperationException("Private key not found for decryption", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Decryption failed: {ex.Message}", ex);
            }
            return result;
        }

        private static void VerifySignature(MdnVerificationResult result, Org.BouncyCastle.X509.X509Certificate partnerCert, MultipartSigned multipartSigned)
        {
            MemoryStream contentStream, sigStream;
            var signedPart = multipartSigned[0];

            var signaturePart = multipartSigned[1] as MimePart;
            contentStream = new MemoryStream();
            signedPart.WriteTo(contentStream);
            var contentBytes = contentStream.ToArray();
            sigStream = new MemoryStream();
            signaturePart.Content.DecodeTo(sigStream);
            var signatureBytes = sigStream.ToArray();

            // Step 3: Parse and verify CMS signature
            var cms = new CmsSignedData(new CmsProcessableByteArray(contentBytes), signatureBytes);
            var signer = cms.GetSignerInfos().GetSigners().Cast<SignerInformation>().FirstOrDefault();

            if (signer != null && signer.Verify(partnerCert))
            {
                result.IsSignatureValid = true;
            }
            else
            {
                result.IsSignatureValid = false;
            }
        }

        private static void GetDisposition(MdnVerificationResult result, MessageDispositionNotification mdnReport)
        {

            if (mdnReport != null)
            {
                var dispositionField = mdnReport.Fields["Disposition"];

                if (!string.IsNullOrEmpty(dispositionField))
                {
                    var parts = dispositionField.Split(';');

                    var dispositionType = parts.Length > 1 ? parts[1].Trim() : string.Empty;
                    var dispositionMain = dispositionType.Split('/')[0].Trim(); // e.g., "processed"
                    var dispositionModifier = dispositionType.Contains("/") ? dispositionType.Split('/')[1].Trim() : null;

                    result.Disposition = dispositionMain;
                    result.DispositionModifier = dispositionModifier;
                    var isMdnProcessed = dispositionMain.Equals("processed", StringComparison.OrdinalIgnoreCase);
                }
                else
                {
                    result.Disposition = "Error";
                    result.DispositionModifier = "No Disposition Field Found";
                }
            }
        }

        private  bool CheckMic(MdnVerificationResult result, MessageDispositionNotification mdnReport)
        {
            

            if (mdnReport != null)
            {
                var dispositionField = mdnReport.Fields["Received-Content-MIC"];

                if (!string.IsNullOrEmpty(dispositionField))
                {
                    var parts = dispositionField.Split(',');

                    var mic = parts.Length > 1 ? parts[0].Trim() : string.Empty;
                    //var micHash = mic.Split('/')[0].Trim(); // e.g., "processed"
                    //var micType = mic.Contains("/") ? mic.Split('/')[1].Trim() : null;

                    bool validMic = _micHandler.ValidateMic(mic, result.OriginalMessageId);

                    return validMic;
                }

                return false;

            }

            return false;

        }
        private Org.BouncyCastle.X509.X509Certificate ParseCertificate(string base64)
        {
            var rawCert = Convert.FromBase64String(base64);
            return new X509CertificateParser().ReadCertificate(rawCert);
        }

        private void HandleMultipartSigned(MdnVerificationResult result, MultipartSigned multipartSigned, Org.BouncyCastle.X509.X509Certificate partnerCert)
        {

            if (multipartSigned.Count != 0 && multipartSigned[0] is MultipartReport report)
            {
                var plainTextPart = report.OfType<TextPart>().FirstOrDefault(p => p.IsPlain);
                if (plainTextPart != null)
                {
                    var plainTextContent = plainTextPart.Text;
                    result.MDNTextPart = plainTextContent;
                }
                if (report is Multipart reportMultipart)
                {
                    var mdnReport = reportMultipart.OfType<MessageDispositionNotification>().FirstOrDefault();
                    var originalMessageIdHeader = mdnReport.Fields.FirstOrDefault(h => h.Field.Equals("Original-Message-ID", StringComparison.OrdinalIgnoreCase));
                    if (originalMessageIdHeader != null)
                    {
                        result.OriginalMessageId = originalMessageIdHeader.Value;
                    }

                }
                GetDisposition( result, report.OfType<MessageDispositionNotification>().FirstOrDefault());

                if (CheckMic( result, report.OfType<MessageDispositionNotification>().FirstOrDefault()))
                {
                    result.IsMicValid = true;

                }
                else
                {
                    result.IsMicValid = false;
                    result.Error = "MIC validation failed";
                }

            }
            else
            {
                result.Disposition = "Error";
                result.DispositionModifier = "No MultipartReport found in MultiPart signed message";
            }

            VerifySignature(result, partnerCert, multipartSigned);
        }

        private void HandleSignedPkcs7(MdnVerificationResult result, ApplicationPkcs7Mime signedData)
        {
            var ctx = new TemporarySecureMimeContext();

            var signatures = signedData.Verify(ctx, out MimeEntity signedContent);
            if (signatures != null && signatures.Count > 0 && signatures.All(sig => sig.Verify()))
            {
                result.IsSignatureValid = true;
                ExtractMdnText(result, signedContent);
            }
            else
            {
                result.IsSignatureValid = false;
                result.Error = "Signature verification failed";
            }
        }

        private void HandleEncryptedPkcs7(MdnVerificationResult result, ApplicationPkcs7Mime encryptedData)
        {
            var ctx = new TemporarySecureMimeContext();
            var decrypted = encryptedData.Decrypt(ctx);
            result.IsDecrypted = decrypted != null;

            if (decrypted is ApplicationPkcs7Mime signedData && signedData.SecureMimeType == SecureMimeType.SignedData)
            {
                var signatures = signedData.Verify(ctx, out MimeEntity signedContent);
                if (signatures != null && signatures.Count > 0 && signatures.All(sig => sig.Verify()))
                {
                    result.IsSignatureValid = true;
                    ExtractMdnText(result, signedContent);
                }
                else
                {
                    result.IsSignatureValid = false;
                    result.Error = "Signature verification failed";
                }
            }
            else
            {
                result.IsSignatureValid = false;
                result.Error = "MDN is not signed";
            }
        }

        private void HandleRawDecryptedContent(MdnVerificationResult result, MimeEntity content)
        {
            result.IsDecrypted = true;

            if (content is ApplicationPkcs7Mime signedData && signedData.SecureMimeType == SecureMimeType.SignedData)
            {
                var ctx = new TemporarySecureMimeContext();
                var signatures = signedData.Verify(ctx, out MimeEntity signedContent);
                if (signatures != null && signatures.Count > 0 && signatures.All(sig => sig.Verify()))
                {
                    result.IsSignatureValid = true;
                    ExtractMdnText(result, signedContent);
                }
                else
                {
                    result.IsSignatureValid = false;
                    result.Error = "Signature verification failed";
                }
            }
            else
            {
                result.IsSignatureValid = false;
                result.Error = "MDN is not signed";
            }
        }

        private void ExtractMdnText(MdnVerificationResult result, MimeEntity content)
        {
            if (content is TextPart textPart)
            {
                result.MdnText = textPart.Text;
                ParseMdnContent(result);
            }
            else if (content is Multipart multipart)
            {
                foreach (var part in multipart.OfType<TextPart>())
                {
                    if (part.ContentType?.MediaSubtype?.Contains("disposition-notification", StringComparison.OrdinalIgnoreCase) == true)
                    {
                        result.MdnText = part.Text;
                        ParseMdnContent(result);
                        break;
                    }
                }
            }
        }



        private void ParseMdnContent(MdnVerificationResult result)
        {
            if (string.IsNullOrEmpty(result.MdnText))
                return;

            var lines = result.MdnText.Split('\n');
            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                if (trimmedLine.StartsWith("Original-Message-ID:", StringComparison.OrdinalIgnoreCase))
                {
                    result.OriginalMessageId = trimmedLine
                        .Substring("Original-Message-ID:".Length)
                        .Trim(' ', '<', '>');
                }
                else if (trimmedLine.StartsWith("Disposition:", StringComparison.OrdinalIgnoreCase))
                {
                    // Extract disposition type (processed/failed/error)
                    if (trimmedLine.Contains("processed/error"))
                        result.Disposition = trimmedLine.Substring("Disposition:".Length).Trim();
                    else if (trimmedLine.Contains("processed"))
                        result.Disposition = trimmedLine.Substring(trimmedLine.IndexOf("processed"));
                    else if (trimmedLine.Contains("failed"))
                        result.Disposition = trimmedLine.Substring(trimmedLine.IndexOf("failed"));
                    else if (trimmedLine.Contains("error"))
                        result.Disposition = trimmedLine.Substring(trimmedLine.IndexOf("error"));
                }
            }
        }
    }
}