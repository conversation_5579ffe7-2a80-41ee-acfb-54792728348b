﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("Serial")]
    public class Serial
    {
        [Key]
        public long IDSerials { get; set; }

        public int IDEvent { get; set; }

        [Required]
        [StringLength(20)]
        public string SerialNumber { get; set; }

        public short? IDPackState { get; set; }
    }

}
