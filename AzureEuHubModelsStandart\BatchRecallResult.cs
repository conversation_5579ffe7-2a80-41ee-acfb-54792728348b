﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("BatchRecallResult")]
    public class BatchRecallResult
    {
        [Key]
        public long IDBatchRecallResult { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime Timestamp { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        [StringLength(50)]
        public string SourceType { get; set; }

        [StringLength(50)]
        public string SourceCode { get; set; }

        [StringLength(20)]
        public string RecallCount { get; set; }

        [StringLength(10)]
        public string ErrorCode { get; set; }

        public string ErrorDescription { get; set; }
    }
}
