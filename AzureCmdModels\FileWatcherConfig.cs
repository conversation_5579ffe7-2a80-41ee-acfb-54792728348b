﻿using SGAuditTrail;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace AzureCmdModels
{
    [Table("FileWatcherConfig")]
    [Audit(DisplayName = "FileWatcher Configuration")]
    public class FileWatcherConfig
    {
        public FileWatcherConfig()
        {
        }

        public FileWatcherConfig(FileWatcherConfig fileWatcherConfig)
        {
            IDFileWatcherConfig = fileWatcherConfig.IDFileWatcherConfig;
            InputFile = fileWatcherConfig.InputFile;

        }

        [Key]
        public int IDFileWatcherConfig { get; set; }

        [StringLength(128)]
        public string FolderID { get; set; }

        public bool FolderEnabled { get; set; }

        /// <summary>
        /// Description of the type of files and folder location
        /// </summary>
        [StringLength(255)]
        public string FolderDescription { get; set; }

        /// <summary>Filter to select the type of files to be monitored.
        /// (Examples: *.txt, *.xml, *.*)</summary>
        [StringLength(128)]
        public string FolderFilter { get; set; }
         
        /// <summary>Full path to be monitored (i.e.: C:\Sftp\LineCode or \\satt.local\Shares\ftp\PRD )</summary>
        [StringLength(255)]
        public string FolderPath { get; set; } //TODO

        /// <summary>If TRUE: the folder and its subfolders will be monitored</summary>
        public bool FolderIncludeSub { get; set; }

        [StringLength(255)]
        public string ExecutableFile { get; set; } //TODO

        [StringLength(500)]
        public string ExecutableArguments { get; set; } //TODO


        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        //TODO: Newly added:
        public int IDCustomer { get; set; } 

        [Required]
        [StringLength(255)]
        public string Username { get; set; }

        [Required]
        public int? IDFileEventType { get; set; }

        [Required]
        [StringLength(255)]
        public string InputFile { get; set; }

        [Required]
        [StringLength(500)]
        public string FullFilePath { get; set; }

        [Required]
        [StringLength(255)]
        public string FolderStructure { get; set; } 

        [InverseProperty("FileWatcherConfigs")]
        public FileEventType FileEventType { get; set; }

        [InverseProperty("FileWatcherConfigs")]
        public Customer Customer { get; set; }

    }
}
