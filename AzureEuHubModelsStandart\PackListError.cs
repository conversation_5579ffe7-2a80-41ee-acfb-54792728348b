﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("PackListError")]
    public class PackListError
    {
        [Key]
        public long IDPackListError { get; set; }

        [Required]
        public long IDPackError { get; set; }

        [Required]
        [StringLength(10)]
        public string Code { get; set; }

        [StringLength(20)]
        public string SerialNumber { get; set; }

    }
}
