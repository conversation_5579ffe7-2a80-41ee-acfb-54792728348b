﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("EmvoCertificate")]

    public class EmvoCertificate
    {

        [Key]
        public int IDEmvoCertificate { get; set; }

        public bool IsServerCertificate { get; set; }

        public bool IsActive { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime ExpiryDate { get; set; }

        [StringLength(50)]
        public string Thumbprint { get; set; }

        public string Data { get; set; }
    }
}
