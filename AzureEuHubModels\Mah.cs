﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("Mah")]
    public class Mah
    {
        [Key]
        public int IDMah { get; set; }

        [Required]
        public int? IDMahType { get; set; }

        [StringLength(50)]
        public string MahID { get; set; }

        [Required]
        [StringLength(100)]
        public string MahName { get; set; }

        [StringLength(255)]
        public string MahStreet { get; set; }

        [StringLength(255)]
        public string MahStreet2 { get; set; }

        [StringLength(100)]
        public string MahCity { get; set; }

        [StringLength(50)]
        public string MahPostCode { get; set; }

        public string MahCountryCode { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [Required]
        public bool IsActive { get; set; }

        [InverseProperty("Mahs")]
        public MahType MahType { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((Mah)obj).IDMah == this.IDMah)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }
    }
}
