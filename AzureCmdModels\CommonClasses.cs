﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using System.Xml;
using System.Text.Json.Serialization;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureCmdModels
{
   
  
    public class AzureModel
    {
        public string Instance { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string Domain { get; set; }
        public string TenantId { get; set; }
    }

    public class DropDownItem
    {
        public int Key { get; set; }

        public string Value { get; set; }

        public string StringKey { get; set; }

        public bool IsSelected { get; set; }

        public int? IntValue { get; set; }
    }

    public class ServiceUserLoginView
    {
        public int IDService { get; set; }

        public string Name { get; set; }

        public bool IsSelected { get; set; }
        public bool ServiceSelected { get; set; }

        public int? IDServiceUserLogin { get; set; }

        public bool IsExternalCodeAccepted { get; set; }

        [StringLength(250)]
        public string ExternalRecieverCode { get; set; }

        [StringLength(250)]
        public string ExternalSenderCode { get; set; }
    }

    public class ServiceRoleView
    {
        public int IDServiceRole { get; set; }

        public string LgnName { get; set; }

        public string Name { get; set; }

        public int? IDUserLoginServiceRole { get; set; }

        public bool IsSelected { get; set; }

    }

    public class AzureAdOptions
    {
        public string ClientId { get; set; }
        public string TenantId { get; set; }
        public string ClientSecret { get; set; }
    }

    public class ImportProductResult()
    {
        public int? IDProduct { get; set; }
        public string Name { get; set; }
        public string GTIN { get; set; }
        public string ResultMessage { get; set; }

        public ImportProductResultType ResultType { get; set; }
    }

    public enum ImportProductResultType
    {
        Success,
        Warning,
        Error
    }

    public enum SerialTypes
    {
        SERIAL,
        SSCC
    }
    [Serializable()]
    public class ProductXML
    {
        public string Name { get; set; }

        public string SrcSystem { get; set; }

        public string CodeType { get; set; }

        public string Code { get; set; }

        public string InternalCodeType { get; set; }

        public string InternalCode { get; set; }

        public string CommonName { get; set; }

        public string Form { get; set; }

        public string Strength { get; set; }

        public int PackSize { get; set; }

        public string PackType { get; set; }

        public string SNXSource { get; set; }

        public string SerializationType { get; set; }

        public string PartnerName { get; set; }

        [XmlElement("Active")]
        public bool Status { get; set; }

        [XmlArrayItem("AdditionalCode", typeof(AdditionalCodeXML))]
        public List<AdditionalCodeXML> AdditionalCodes { get; set; }
    }

    [Serializable()]
    public class AdditionalCodeXML
    {
        public string CodeType { get; set; }

        public string Code { get; set; }
    }

    [Serializable()]
    [XmlRoot("DOC")]
    public class ProductsXML
    {
        [XmlArrayItem("Product", typeof(ProductXML))]
        public List<ProductXML> ProductXmlList { get; set; }

        public byte[] GetXmlBytes()
        {
            XmlSerializer serializer = new XmlSerializer(typeof(ProductsXML));

            using (MemoryStream memoryStream = new MemoryStream())
            {
                using (XmlWriter xmlWriter = XmlWriter.Create(memoryStream))
                {
                    serializer.Serialize(xmlWriter, this);

                    return memoryStream.ToArray();
                }
            }
        }

        public bool LoadFromStream(Stream stream)
        {
            try
            {
                XmlSerializer serializer = new XmlSerializer(this.GetType());
                using (StreamReader reader = new StreamReader(stream))
                {
                    var xml = (ProductsXML)serializer.Deserialize(reader);

                    this.ProductXmlList = xml.ProductXmlList
                        .Select(p => new ProductXML()
                        {
                            Name = p.Name,
                            AdditionalCodes = p.AdditionalCodes,
                            SerializationType = p.SerializationType,
                            SNXSource = p.SNXSource,
                            SrcSystem = p.SrcSystem,
                            Status = p.Status,
                            Strength = p.Strength,
                            Code = p.Code,
                            CodeType = p.CodeType,
                            InternalCode = p.InternalCode,
                            InternalCodeType = p.InternalCodeType,
                            CommonName = p.CommonName,
                            PackSize = p.PackSize,
                            Form = p.Form,
                            PackType = p.PackType,
                            PartnerName = p.PartnerName
                        })
                        .ToList();
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
    [Serializable()]
    public class CustomerPartnerXml
    {
        [XmlElement("Name")]
        public string Name { get; set; }

        [XmlElement("SystemName")]
        public string SystemName { get; set; }

        [XmlElement("SNXRequest")]
        public string SNXRequest { get; set; }

        [XmlElement("SSCCRequest")]
        public string SSCCRequest { get; set; }

        [XmlElement("EBRImport")]
        public string EBRImport { get; set; }

        [XmlElement("BatchReport")]
        public string BatchReport { get; set; }
        [XmlElement("ShipmentReport")]
        public string ShipmentReport { get; set; }

        [XmlElement("Active")]
        public bool IsActive { get; set; }

        [XmlArrayItem("Parameters", typeof(CustomerPartnerParam))]
        public List<CustomerPartnerParam> CustomerPartnerParams { get; set; }
    }

    [Serializable()]
    public class CustomerPartnerParam
    {
        [XmlElement("Key")]
        public string Key { get; set; }

        [XmlElement("Value")]
        public string Value { get; set; }
    }

    [Serializable()]
    [XmlRoot("DOC")]
    public class CustomerPartnerXmlList
    {

        [XmlArrayItem("CustomerPartner", typeof(CustomerPartnerXml))]
        public List<CustomerPartnerXml> CustomerPartnersList { get; set; }

        public byte[] GetXmlBytes()
        {
            XmlSerializer serializer = new XmlSerializer(typeof(CustomerPartnerXmlList));

            using (MemoryStream memoryStream = new MemoryStream())
            {
                using (XmlWriter xmlWriter = XmlWriter.Create(memoryStream))
                {
                    serializer.Serialize(xmlWriter, this);

                    return memoryStream.ToArray();
                }
            }
        }

        public bool LoadFromStream(Stream stream)
        {
            try
            {
                XmlSerializer serializer = new XmlSerializer(this.GetType());
                using (StreamReader reader = new StreamReader(stream))
                {
                    var xml = (CustomerPartnerXmlList)serializer.Deserialize(reader);

                    this.CustomerPartnersList = xml.CustomerPartnersList
                        .Select(p => new CustomerPartnerXml()
                        {
                            Name = p.Name,
                            SystemName = p.SystemName,
                            SNXRequest = p.SNXRequest,
                            SSCCRequest = p.SSCCRequest,
                            EBRImport = p.EBRImport,
                            BatchReport = p.BatchReport,
                            ShipmentReport = p.ShipmentReport,
                            IsActive = p.IsActive,
                            CustomerPartnerParams = p.CustomerPartnerParams
                                .Select(e => new CustomerPartnerParam()
                                {
                                    Key = e.Key,
                                    Value = e.Value
                                })
                                .ToList()
                        }).ToList();
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }


    [Serializable()]
    [XmlRoot("ArrayOfCustomFolderSettings")]
    public class FileWatcherConfigXmlList
    {

        [XmlArrayItem("CustomFolderSettings", typeof(FileWatcherConfigXml))]
        public List<FileWatcherConfigXml> FileWatcherConfigs { get; set; }

        public byte[] GetXmlBytes()
        {
            XmlSerializer serializer = new XmlSerializer(typeof(FileWatcherConfigXmlList));

            using (MemoryStream memoryStream = new MemoryStream())
            {
                using (XmlWriter xmlWriter = XmlWriter.Create(memoryStream))
                {
                    serializer.Serialize(xmlWriter, this);

                    return memoryStream.ToArray();
                }
            }
        }

        public bool LoadFromStream(Stream stream)
        {
            try
            {
                XmlSerializer serializer = new XmlSerializer(this.GetType());
                using (StreamReader reader = new StreamReader(stream))
                {
                    var xml = (FileWatcherConfigXmlList)serializer.Deserialize(reader);

                    this.FileWatcherConfigs = xml.FileWatcherConfigs
                        .Select(p => new FileWatcherConfigXml()
                        {
                            IDFileWatcherConfig = p.IDFileWatcherConfig,
                            FolderDescription = p.FolderDescription,
                            ExecutableArguments = p.ExecutableArguments,
                            ExecutableFile = p.ExecutableFile,
                            FolderEnabled = p.FolderEnabled,
                            FolderFilter = p.FolderFilter,
                            FolderID = p.FolderID,
                            FolderIncludeSub = p.FolderIncludeSub,
                            FolderPath = p.FolderPath,
                            TimestampCreated = p.TimestampCreated,
                            TimestampUpdated = p.TimestampUpdated,
                            UserCreated = p.UserCreated,
                            UserUpdated = p.UserUpdated
                        }).ToList();
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }

    [Serializable()]
    public class FileWatcherConfigXml
    {
        public FileWatcherConfigXml() 
        {
            FileWatcherConfigs = new List<FileWatcherConfigXml>();
        }

        public FileWatcherConfigXml(FileWatcherConfig fileWatcherConfig)
        {
            if(fileWatcherConfig != null && fileWatcherConfig.IDFileEventType.HasValue)
            {
                IDFileWatcherConfig = fileWatcherConfig.IDFileWatcherConfig;
                FolderDescription = fileWatcherConfig.FolderDescription;
                ExecutableArguments = fileWatcherConfig.ExecutableArguments;
                ExecutableFile = fileWatcherConfig.ExecutableFile;
                FolderEnabled = fileWatcherConfig.FolderEnabled;
                FolderFilter = fileWatcherConfig.FolderFilter;
                FolderID = fileWatcherConfig.FolderID;
                FolderIncludeSub = fileWatcherConfig.FolderIncludeSub;
                FolderPath = fileWatcherConfig.FolderPath;
                FolderStructure = fileWatcherConfig.FolderStructure;
                FullFilePath = fileWatcherConfig.FullFilePath;
                IDCustomer = fileWatcherConfig.IDCustomer;
                IDEventType = fileWatcherConfig.IDFileEventType.Value;
                InputFile = fileWatcherConfig.InputFile;
                Username = fileWatcherConfig.Username;
                TimestampCreated = fileWatcherConfig.TimestampCreated;
                TimestampUpdated = fileWatcherConfig.TimestampUpdated;
                UserCreated = fileWatcherConfig.UserCreated;
                UserUpdated = fileWatcherConfig.UserUpdated;
            }
        }

        public FileWatcherConfig ToFileWatcherConfig()
        {
            FileWatcherConfig result = new FileWatcherConfig()
            {
                IDFileWatcherConfig = IDFileWatcherConfig,
                FolderDescription = FolderDescription,
                ExecutableArguments = ExecutableArguments,
                ExecutableFile = ExecutableFile,
                FolderEnabled = FolderEnabled,
                FolderFilter = FolderFilter,
                FolderID = FolderID,
                FolderIncludeSub = FolderIncludeSub,
                FolderPath = FolderPath,
                FolderStructure = FolderStructure,
                FullFilePath = FullFilePath,
                IDCustomer = IDCustomer,
                IDFileEventType = IDEventType,
                InputFile = InputFile,
                Username = Username,
                TimestampCreated = TimestampCreated,
                TimestampUpdated = TimestampUpdated,
                UserCreated = UserCreated,
                UserUpdated = UserUpdated
            };

            return result;
        }

        [XmlArrayItem("FileWatcherConfigs", typeof(FileWatcherConfigXml))]
        public List<FileWatcherConfigXml> FileWatcherConfigs { get; set; }

        [XmlElement("IDFileWatcherConfig")]
        public int IDFileWatcherConfig { get; set; }

        [XmlElement("FolderID")]
        public string FolderID { get; set; }

        [XmlElement("FolderEnabled")]
        public bool FolderEnabled { get; set; }

        [XmlElement("FolderDescription")]
        public string FolderDescription { get; set; }

        [XmlElement("FolderFilter")]
        public string FolderFilter { get; set; }

        [XmlElement("FolderPath")]
        public string FolderPath { get; set; }

        [XmlElement("FolderIncludeSub")]
        public bool FolderIncludeSub { get; set; }

        [XmlElement("ExecutableFile")]
        public string ExecutableFile { get; set; }

        [XmlElement("ExecutableArguments")]
        public string ExecutableArguments { get; set; }

        [XmlElement("IDCustomer")]
        public int IDCustomer { get; set; }

        [XmlElement("Username")]
        public string Username { get; set; }

        [XmlElement("IDEventType")]
        public int IDEventType { get; set; }

        [XmlElement("InputFile")]
        public string InputFile { get; set; }

        [XmlElement("FullFilePath")]
        public string FullFilePath { get; set; }

        [XmlElement("FolderStructure")]
        public string FolderStructure { get; set; }

        [XmlElement("TimestampCreated")]
        public DateTime TimestampCreated { get; set; }

        [XmlElement("UserCreated")]
        public string UserCreated { get; set; }

        [XmlElement("TimestampUpdated")]
        public DateTime? TimestampUpdated { get; set; }

        [XmlElement("UserUpdated")]
        public string UserUpdated { get; set; }

        public byte[] GetXmlBytes()
        {
            XmlSerializer serializer = new XmlSerializer(typeof(FileWatcherConfigXml));

            using (MemoryStream memoryStream = new MemoryStream())
            {
                using (XmlWriter xmlWriter = XmlWriter.Create(memoryStream))
                {
                    serializer.Serialize(xmlWriter, this);

                    return memoryStream.ToArray();
                }
            }
        }

    }

    [Serializable()]
    public class RouterConfigurationXML
    {
        [XmlElement("IDCustomer")]
        public int IDCustomer { get; set; }
        
        [XmlAttribute("IsActive")]
        public bool IsActive { get; set; }

        [XmlElement("RouterElementName")]
        public string ElementName { get; set; }

        [XmlElement("RouterElementValues")]
        public List<string> RouterElementValues { get; set; }

        [XmlAttribute("ProcessorType")]
        public ProcessorTypeEnum ProcessorType { get; set; }

        [XmlElement("SourceFolderAccess")]
        public FolderAccess SourceFolderAccess { get; set; }

        [XmlElement("SourceUrl")]
        public string SourceUrl { get; set; }

        [XmlElement("SourcePort")]
        public int? SourcePort { get; set; }

        [XmlElement("SourceUser")]
        public string SourceUser { get; set; }

        [XmlElement("SourcePassword")]
        public string SourcePassword { get; set; }

        [XmlElement("SourceFolder")]
        public string SourceFolder { get; set; }

        [XmlElement("TargetFolderAccess")]
        public FolderAccess TargetFolderAccess { get; set; }

        [XmlElement("TargetUrl")]
        public string TargetUrl { get; set; }

        [XmlElement("TargetPort")]
        public int? TargetPort { get; set; }

        [XmlElement("TargetUser")]
        public string TargetUser { get; set; }

        [XmlElement("TargetPassword")]
        public string TargetPassword { get; set; }

        [XmlElement("TargetFolder")]
        public string TargetFolder { get; set; }

        [XmlElement("AlternateTargetFolder")]
        public string AlternateTargetFolder { get; set; }
               
    }

    [Serializable()]
    [XmlRoot("DOC")]
    public class RouterConfigurationsXml
    {

        [XmlArrayItem("FolderList", typeof(RouterConfigurationXML))]
        public List<RouterConfigurationXML> RouterConfigurations { get; set; }

        public byte[] GetXmlBytes()
        {
            XmlSerializer serializer = new XmlSerializer(typeof(RouterConfigurationsXml));

            using (MemoryStream memoryStream = new MemoryStream())
            {
                using (XmlWriter xmlWriter = XmlWriter.Create(memoryStream))
                {
                    serializer.Serialize(xmlWriter, this);

                    return memoryStream.ToArray();
                }
            }
        }

        public bool LoadFromStream(Stream stream)
        {
            try
            {
                XmlSerializer serializer = new XmlSerializer(this.GetType());
                using (StreamReader reader = new StreamReader(stream))
                {
                    var xml = (RouterConfigurationsXml)serializer.Deserialize(reader);

                    this.RouterConfigurations = xml.RouterConfigurations
                        .Select(r => new RouterConfigurationXML()
                        {
                            ProcessorType = r.ProcessorType,
                            IsActive = r.IsActive,
                            IDCustomer = r.IDCustomer,
                            ElementName = r.ElementName,
                            RouterElementValues = r.RouterElementValues
                                .Select(value => value)
                                .ToList(),
                            SourceFolderAccess = r.SourceFolderAccess,
                            SourceUrl = r.SourceUrl,
                            SourcePort = r.SourcePort,
                            SourceUser = r.SourceUser,
                            SourcePassword = r.SourcePassword,
                            SourceFolder = r.SourceFolder,
                            TargetFolderAccess = r.TargetFolderAccess,
                            TargetUrl = r.TargetUrl,
                            TargetPort = r.TargetPort,
                            TargetUser = r.TargetUser,
                            TargetPassword = r.TargetPassword,
                            TargetFolder = r.TargetFolder,
                            AlternateTargetFolder = r.AlternateTargetFolder
                        }).ToList();
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }


}
