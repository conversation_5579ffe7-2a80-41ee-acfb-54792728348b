using System;
using System.IO;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AS2App.Models.ConfigModels;
using Microsoft.EntityFrameworkCore;

namespace AS2App.Features.Services.AzureLogStorageService
{

    public class AzureLogStorageService : IAzureLogStorageService
    {
        private readonly ILogger<AzureLogStorageService> _logger;
        public AzureAs2ProxyModels.DBModel _azureAs2ProxyDBModel;

        public AzureLogStorageService(
            ILogger<AzureLogStorageService> logger,
            AzureAs2ProxyModels.DBModel azureAs2ProxyDBModel)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _azureAs2ProxyDBModel = azureAs2ProxyDBModel ?? throw new ArgumentNullException(nameof(azureAs2ProxyDBModel));

        }

        public void LogObjectToStorage<T>(T obj, string fileNamePrefix, string messageId)
        {
            try
            {
                // Cache app settings from DB to avoid repeated queries
                var settings = _azureAs2ProxyDBModel.AppSettings
                    .AsNoTracking()
                    .ToDictionary(s => s.Name, s => s.Value, StringComparer.OrdinalIgnoreCase);

                if (!settings.TryGetValue("DEBUG_LOG", out var debugLogValue) || !bool.TryParse(debugLogValue, out var isDebugEnabled) || !isDebugEnabled)
                {
                    _logger.LogInformation($"Azure storage logging is disabled. Not logging {fileNamePrefix} to Azure storage.");
                    return;
                }

                if (!settings.TryGetValue("AzureStorageSystem", out var storageSystem) ||
                    !Enum.TryParse(storageSystem, out Softgroup.FileStorage.StorageTypeEnum storageType))
                {
                    _logger.LogError($"Unsupported or missing file storage system: [{storageSystem}]");
                    return;
                }

                if (!settings.TryGetValue("AzureStorageConnectionString", out var azureStorageConnectionString) ||
                    string.IsNullOrWhiteSpace(azureStorageConnectionString))
                {
                    _logger.LogError("Azure Storage Connection String is not configured.");
                    return;
                }

                string datePart = DateTime.Now.ToString("yyyy-MM-dd");
                string timePart = DateTime.Now.ToString("yyyyMMdd-HHmmss");
                string sanitizedMessageId = messageId.Replace("<", "").Replace(">", "");
                string azureFilePath = $@"/as2-azure-{datePart}/{fileNamePrefix}-{sanitizedMessageId}-{timePart}.json";

                using var stream = new MemoryStream();
                JsonSerializer.Serialize(stream, obj);
                stream.Position = 0;

                Softgroup.FileStorage.FileStorage.Save(storageType, stream, azureFilePath, azureStorageConnectionString);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to log {fileNamePrefix} to Azure storage");
            }
        }

    }
}