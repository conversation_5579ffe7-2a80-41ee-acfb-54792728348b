﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{

    [Table("PackVerificationList")]
    public class PackVerificationList
    {
        [Key]
        public long IDPackVerificationList { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [StringLength(20)]
        public string SerialNumber { get; set; }

        [StringLength(50)]
        public string State { get; set; }
    }
}
