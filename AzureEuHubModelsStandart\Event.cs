﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("Event")]
    public class Event
    {

        [Key]
        public int IDEvent { get; set; }

        public int IDBatch { get; set; }

        public int IDEventType { get; set; }

        [Required]
        [StringLength(50)]
        public string HubCorrelationID { get; set; }

        [StringLength(20)]
        public string NewBatchID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? NewExpiryDate { get; set; }

        public bool? NewExpDateYYMM { get; set; }

        [StringLength(50)]
        public string DistributionState { get; set; }

        public int? SourceState { get; set; }

        public int? TargetState { get; set; }

        [StringLength(512)]
        public string RecallInformation { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? RecallEffectiveDate { get; set; }

        public bool Info { get; set; }
        public bool Warrning { get; set; }
        public bool Error { get; set; }
        public bool Canceled { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        public int? MasterIDEvent { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? AutoPublishTime { get; set; }
    }
}
