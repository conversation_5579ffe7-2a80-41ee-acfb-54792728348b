﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("PackVerificationStatus")]
    public partial class PackVerificationStatus
    {
        [Key]
        public short IDPackVerificatinStatus { get; set; }

        [StringLength(50)]
        public string HubName { get; set; }

        [StringLength(50)]
        public string Name { get; set; }
    }
}
