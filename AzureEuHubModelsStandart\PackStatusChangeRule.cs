﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("PackStatusChangeRule")]
    public class PackStatusChangeRule
    {
        [Key]
        public short IDSourceStatus { get; set; }

        public short IDTargetStatus { get; set; }

        public short IDCustomerType { get; set; }

        public bool OneSerialOnly { get; set; }
    }
}
