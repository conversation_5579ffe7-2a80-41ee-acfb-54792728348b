﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    public class AlertApiRequest
    {
        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? Count { get; set; }

        public int? Skip { get; set; }
    }

    public class AlertApiResult
    {
        public List<AlertApi> Alerts { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int Count { get; set; }

        public int Total { get; set; }

        public int Skipped { get; set; }

        public bool IsError { get; set; }

        public string ErrorMessage { get; set; }
    }

    public class AlertApi
    {
        public int ID { get; set; }

        public DateTime? Timestamp { get; set; }

        public string ProductCodeType { get; set; }

        public string ProductCode { get; set; }

        public string Code { get; set; }

        public string UniqueAlertID { get; set; }

        public string Message { get; set; }

        public string Source { get; set; }

        public List<AlertItemApi> Items { get; set; }
    }


    public class AlertItemApi
    {
        public string Name { get; set; }

        public string Value { get; set; }
    }
}
