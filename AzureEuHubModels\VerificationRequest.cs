﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("VerificationRequest")]
    public class VerificationRequest
    {
        public VerificationRequest()
        {
            VerificationRequestItems = new HashSet<VerificationRequestItem>();
        }

        [Key]
        public int IDVerificationRequest { get; set; }

        [Required]
        [StringLength(20)]
        public string ProductCode { get; set; }

        [Required]
        [StringLength(20)]
        public string CodeType { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public Guid? ReferenceID { get; set; }

        public DateTime? ReceivedFromCustomer { get; set; }

        public DateTime? SendToEuHubForVerification { get; set; }

        public DateTime? ReceivedFromEuHub { get; set; }

        [StringLength(20)]
        public string BatchID { get; set; }

        [StringLength(50)]
        public string DistributionState { get; set; }

        public bool IsExpiryDateZeroDay { get; set; }

        [InverseProperty("VerificationRequest")]
        public ICollection<VerificationRequestItem> VerificationRequestItems { get; set; }



        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((VerificationRequest)obj).IDVerificationRequest == this.IDVerificationRequest)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }

    }
}
