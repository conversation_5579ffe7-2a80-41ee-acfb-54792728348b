﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("ResponseStatus")]
    public class ResponseStatus
    {
        [Key]
        public int IDResponseStatus { get; set; }

        [StringLength(30)]
        public string Status { get; set; }
    }
}
