﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    public class ProductCountry
    {
        public int IDProductCountry { get; set; }

        public int IDProduct { get; set; }

        public short IDCountry { get; set; }

        public short? SerializationFlag { get; set; }

        public DateTime TimeStampCreated { get; set; }

        public DateTime? TimeStampUpdated { get; set; }

        public string UserCreated { get; set; }

        public string UserUpdated { get; set; }

        public Country Country { get; set; }

    }
}
