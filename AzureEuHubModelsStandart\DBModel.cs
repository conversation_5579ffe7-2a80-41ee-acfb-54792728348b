﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Infrastructure.DependencyResolution;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [DbConfigurationType(typeof(EuDbContextConfiguration))]
    public class DBModel : DbContext
    {
        //define classed here
        public virtual DbSet<AppLog> AppLogs { get; set; }
        public virtual DbSet<AppLogItem> AppLogItems { get; set; }
        public virtual DbSet<Alert> Alerts { get; set; }
        public virtual DbSet<AlertItem> AlertItems { get; set; }
        public virtual DbSet<AlternateProduct> AlternateProducts { get; set; }
        public virtual DbSet<Batch> Batches { get; set; }
        public virtual DbSet<VerificationRequest> VerificationRequests { get; set; }
        public virtual DbSet<CallbackResult> CallbackResults { get; set; }
        public virtual DbSet<VerificationRequestItem> VerificationRequestItems { get; set; }
        public virtual DbSet<Country> Countries { get; set; }

        public virtual DbSet<Event> Events { get; set; }
        public virtual DbSet<EventType> EventTypes { get; set; }
        public virtual DbSet<LogSecuritySessionToken> LogSecuritySessionTokens { get; set; }
        public virtual DbSet<Market> Markets { get; set; }
        public virtual DbSet<PackState> PackStatuses { get; set; }
        public virtual DbSet<HubPackStatus> HubPackStatuses { get; set; }
        public virtual DbSet<PackStatusChangeRule> PackStatusChangeRules { get; set; }
        public virtual DbSet<Serial> Serials { get; set; }
        public virtual DbSet<AddSerialResult> AddSerialResults { get; set; }
        public virtual DbSet<RecallMarketResult> RecallMarketResults { get; set; }
        public virtual DbSet<PackVerificationStatus> PackVerificationStatuses { get; set; }
        public virtual DbSet<ResponseStatus> ResponseStatuses { get; set; }
        public virtual DbSet<SecuritySessionToken> SecuritySessionTokens { get; set; }
        public virtual DbSet<ProductTargetMarket> ProductTargetMarkets { get; set; }
        public virtual DbSet<Wholesaler> Wholesalers { get; set; }
        public virtual DbSet<ProductHubEvent> ProductHubEvents { get; set; }
        public virtual DbSet<ProductHubEventMarket> ProductHubEventMarkets { get; set; }
        public virtual DbSet<ProductStatus> ProductStatuses { get; set; }
        public virtual DbSet<ProductError> ProductErrors { get; set; }
        public virtual DbSet<Mah> Mahs { get; set; }
        public virtual DbSet<MahType> MahTypes { get; set; }
        public virtual DbSet<PackError> PackErrors { get; set; }
        public virtual DbSet<PackListError> PackListErrors { get; set; }
        public virtual DbSet<PackListErrorDescription> PackListErrorDescriptions { get; set; }
        public virtual DbSet<BatchRecallMarket> BatchRecallMarkets { get; set; }
        public virtual DbSet<BatchRecallResult> BatchRecallResults { get; set; }
        public virtual DbSet<PackVerification> PackVerifications { get; set; }
        public virtual DbSet<PackVerificationList> PackVerificationLists { get; set; }
        public virtual DbSet<PackVerificationError> PackVerificationErrors { get; set; }
        public virtual DbSet<ProductWithdrawalBatch> ProductWithdrawalBatches { get; set; }
        public virtual DbSet<ProductWithdrawalMarket> ProductWithdrawalMarkets { get; set; }
        public virtual DbSet<ProductWithdrawalResult> ProductWithdrawalResults { get; set; }
        public virtual DbSet<AuditTrail> AuditTrails { get; set; }
        public virtual DbSet<EventConfirm> EventConfirms { get; set; }
        public virtual DbSet<HubReport> HubReports { get; set; }
        public virtual DbSet<HubReportParam> HubReportParams { get; set; }
        public virtual DbSet<ReportRequest> ReportRequests { get; set; }
        public virtual DbSet<ReportRequestParam> ReportRequestParams { get; set; }
        public virtual DbSet<ReportReady> ReportReady { get; set; }
        public virtual DbSet<EmvoCertificate> EmvoCertificates { get; set; }
        public virtual DbSet<TargetMarket> TargetMarkets { get; set; }
        public virtual DbSet<BatchProduct> BatchProducts { get; set; }
        public virtual DbSet<BatchTargetMarket> BatchTargetMarkets { get; set; }
        public virtual DbSet<ProductExtend> ProductExtends { get; set; }

        public virtual DbSet<Setting> Settings { get; set; }


        public DBModel()
        : base("name=EuDBModel")
        {
            Database.CommandTimeout = 600;
        }

        public DBModel(string connectionString)
            :base(connectionString)
        {
            Database.CommandTimeout = 600;
            
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            
        }

    }


    public class EuDbContextConfiguration : DbConfiguration
    {
        public EuDbContextConfiguration()
        {
            if (useCachedDbModelStore)
            {
                string cachePath = Path.GetDirectoryName(this.GetType().Assembly.Location);
                MyDbModelStore cachedDbModelStore = new MyDbModelStore(cachePath);
                IDbDependencyResolver dependencyResolver = new SingletonDependencyResolver<DbModelStore>(cachedDbModelStore);
                AddDependencyResolver(dependencyResolver);
                SetModelStore(cachedDbModelStore);
            }
        }

        private static bool useCachedDbModelStore = true;

        public static void Configure(bool useCachedDbModelStore)
        {
            EuDbContextConfiguration.useCachedDbModelStore = useCachedDbModelStore;
        }

        private class MyDbModelStore : DefaultDbModelStore
        {
            public MyDbModelStore(string location)
                    : base(location)
            { }

            public override DbCompiledModel TryLoad(Type contextType)
            {
                string path = GetFilePath(contextType);
                if (File.Exists(path))
                {
                    DateTime lastWriteTime = File.GetLastWriteTimeUtc(path);
                    DateTime lastWriteTimeDomainAssembly = File.GetLastWriteTimeUtc(typeof(DBModel).Assembly.Location);
                    if (lastWriteTimeDomainAssembly > lastWriteTime)
                    {
                        File.Delete(path);
                    }
                }
                else
                {
                }

                var res = base.TryLoad(contextType);
                return res;
            }
        }
    }
}
