﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("ProductWithdrawalMarket")]
    public class ProductWithdrawalMarket
    {
        [Key]
        public int IDProductWithdrawalMarket { get; set; }

        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? Timestamp { get; set; }

        [StringLength(2)]
        public string Market { get; set; }

        public bool Withdraw { get; set; }
    }
}
