﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("VerificationRequest")]
    public class VerificationRequest
    {
        
        [Key]
        public int IDVerificationRequest { get; set; }

        [Required]
        [StringLength(20)]
        public string ProductCode { get; set; }

        [Required]
        [StringLength(20)]
        public string CodeType { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public Guid? ReferenceID { get; set; }

        public DateTime? ReceivedFromCustomer { get; set; }

        public DateTime? SendToEuHubForVerification { get; set; }

        public DateTime? ReceivedFromEuHub { get; set; }

        [StringLength(20)]
        public string BatchID { get; set; }

        [StringLength(50)]
        public string DistributionState { get; set; }

        public bool IsExpiryDateZeroDay { get; set; }

    }
}
