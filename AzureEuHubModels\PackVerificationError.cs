﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;

namespace AzureEuHubModels
{
    [Table("PackVerificationError")]
    public class PackVerificationError
    {
        [Key]
        public long IDPackVerificationError { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [StringLength(20)]
        public string SerialNumber { get; set; }

        [StringLength(10)]
        public string Code { get; set; }

        public string GetDescription(List<PackListErrorDescription> descriptions)
        {
            try
            {
                if (descriptions != null)
                {
                    var descr = descriptions.FirstOrDefault(d => d.Code == this.Code);
                    if(descr != null)
                    {
                        return descr.Description;
                    }
                }
                return "n/a";
            }
            catch (Exception)
            {
                return string.Empty;
                
            }
        }

    }
}
