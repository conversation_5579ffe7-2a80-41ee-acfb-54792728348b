﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("CallbackResult")]
    public class CallbackResult
    {
        [Key]
        public long IDCallbackResult { get; set; }

        [StringLength(512)]
        public string Source { get; set; }

        [StringLength(512)]
        public string Target { get; set; }

        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? Timestamp { get; set; }

        [StringLength(128)]
        public string TypeResponse { get; set; }

        public string CallBackXml { get; set; }

        public string Error { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampCreated { get; set; }


    }
}
