<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-UsageStatisticsAPI-3e10c9b3-9e9b-451c-99ac-da04ddb8a96d</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.7" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.Identity.Web" Version="2.15.2" />
    <PackageReference Include="Microsoft.Identity.Web.DownstreamApi" Version="2.15.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AzureCmdModels\AzureCmdModels.csproj" />
    <ProjectReference Include="..\AzureUsageStatisticsModels\AzureUsageStatisticsModels.csproj" />
  </ItemGroup>

</Project>
