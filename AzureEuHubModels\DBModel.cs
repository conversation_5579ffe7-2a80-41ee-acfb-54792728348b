﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{

    public class DBModel : DbContext
    {
        //define classed here
        public virtual DbSet<AppLog> AppLogs { get; set; }
        public virtual DbSet<AppLogItem> AppLogItems { get; set; }
        public virtual DbSet<Alert> Alerts { get; set; }
        public virtual DbSet<AlertItem> AlertItems { get; set; }
        public virtual DbSet<AlternateProduct> AlternateProducts { get; set; }
        public virtual DbSet<Batch> Batches { get; set; }
        public virtual DbSet<VerificationRequest> VerificationRequests { get; set; }
        public virtual DbSet<CallbackResult> CallbackResults { get; set; }
        public virtual DbSet<VerificationRequestItem> VerificationRequestItems { get; set; }
        public virtual DbSet<Country> Countries { get; set; }

        public virtual DbSet<Event> Events { get; set; }
        public virtual DbSet<EventType> EventTypes { get; set; }
        public virtual DbSet<LogSecuritySessionToken> LogSecuritySessionTokens { get; set; }
        public virtual DbSet<Market> Markets { get; set; }
        public virtual DbSet<PackState> PackStatuses { get; set; }
        public virtual DbSet<HubPackStatus> HubPackStatuses { get; set; }
        public virtual DbSet<PackStatusChangeRule> PackStatusChangeRules { get; set; }
        public virtual DbSet<Serial> Serials { get; set; }
        public virtual DbSet<AddSerialResult> AddSerialResults { get; set; }
        public virtual DbSet<RecallMarketResult> RecallMarketResults { get; set; }
        public virtual DbSet<PackVerificationStatus> PackVerificationStatuses { get; set; }
        public virtual DbSet<ResponseStatus> ResponseStatuses { get; set; }
        public virtual DbSet<SecuritySessionToken> SecuritySessionTokens { get; set; }
        public virtual DbSet<ProductTargetMarket> ProductTargetMarkets { get; set; }
        public virtual DbSet<Wholesaler> Wholesalers { get; set; }
        public virtual DbSet<ProductHubEvent> ProductHubEvents { get; set; }
        public virtual DbSet<ProductHubEventMarket> ProductHubEventMarkets { get; set; }
        public virtual DbSet<ProductStatus> ProductStatuses { get; set; }
        public virtual DbSet<ProductError> ProductErrors { get; set; }
        public virtual DbSet<Mah> Mahs { get; set; }
        public virtual DbSet<MahType> MahTypes { get; set; }
        public virtual DbSet<PackError> PackErrors { get; set; }
        public virtual DbSet<PackListError> PackListErrors { get; set; }
        public virtual DbSet<PackListErrorDescription> PackListErrorDescriptions { get; set; }
        public virtual DbSet<BatchRecallMarket> BatchRecallMarkets { get; set; }
        public virtual DbSet<BatchRecallResult> BatchRecallResults { get; set; }
        public virtual DbSet<PackVerification> PackVerifications { get; set; }
        public virtual DbSet<PackVerificationList> PackVerificationLists { get; set; }
        public virtual DbSet<PackVerificationError> PackVerificationErrors { get; set; }
        public virtual DbSet<ProductWithdrawalBatch> ProductWithdrawalBatches { get; set; }
        public virtual DbSet<ProductWithdrawalMarket> ProductWithdrawalMarkets { get; set; }
        public virtual DbSet<ProductWithdrawalResult> ProductWithdrawalResults { get; set; }
        public virtual DbSet<AuditTrail> AuditTrails { get; set; }
        public virtual DbSet<EventConfirm> EventConfirms { get; set; }
        public virtual DbSet<HubReport> HubReports { get; set; }
        public virtual DbSet<HubReportParam> HubReportParams { get; set; }
        public virtual DbSet<ReportRequest> ReportRequests { get; set; }
        public virtual DbSet<ReportRequestParam> ReportRequestParams { get; set; }
        public virtual DbSet<ReportReady> ReportReady { get; set; }
        public virtual DbSet<EmvoCertificate> EmvoCertificates { get; set; }
        public virtual DbSet<BatchProduct> BatchProducts { get; set; }
        public virtual DbSet<ProductExtend> ProductExtends { get; set; }
        public virtual DbSet<TargetMarket> TargetMarkets { get; set; }


        // CUSTOM MODELS no real table representation, use only with .FromSql() method!
        public virtual DbSet<DbList> DBList { get; set; }
        public virtual DbSet<DbString> DBString { get; set; }
        //public virtual DbSet<CustomModels.PackListErrorSummary> DbPackListErrorSummary { get; set; }


        public virtual DbSet<Setting> Settings { get; set; }

        private readonly string _connectionString;

        public DBModel(string connectionString)
        {
            _connectionString = connectionString;
            Database.SetCommandTimeout(600);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_connectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<AuditTrail>(entity =>
            {
                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
            });


            modelBuilder.Entity<HubReportParam>(entity =>
            {
                entity.HasOne(p => p.HubReport)
                    .WithMany(r => r.HubReportParams)
                    .HasForeignKey(p => p.IDHubReport)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_HubReportParam_HubReport");
            });

            modelBuilder.Entity<ReportRequest>(entity =>
            {
                entity.HasOne(p => p.HubReport)
                    .WithMany(r => r.ReportRequests)
                    .HasForeignKey(p => p.IDHubReport)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ReportRequest_HubReport");

                entity.HasOne(p => p.ReportReady)
                    .WithOne(r => r.ReportRequest)
                    .HasForeignKey<ReportRequest>(e => e.CorrelationID)
                    .IsRequired(false)
                    .OnDelete(DeleteBehavior.ClientSetNull);

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");

            });

            modelBuilder.Entity<ReportRequestParam>(entity =>
            {
                entity.HasOne(p => p.ReportRequest)
                    .WithMany(r => r.ReportRequestParams)
                    .HasForeignKey(p => p.IDReportRequest)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ReportRequestParam_ReportRequest");

                entity.HasOne(p => p.HubReportParam)
                    .WithMany(r => r.ReportRequestParams)
                    .HasForeignKey(p => p.IDHubReportParam)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ReportRequestParam_HubReportParam");
            });



            modelBuilder.Entity<PackError>(entity =>
            {
                entity.HasOne(e => e.PackStatus)
                    .WithMany(s => s.PackErrors)
                    .HasForeignKey(e => e.IDPackStatus)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PackError_PackStatus");
            });

            modelBuilder.Entity<PackListError>(entity =>
            {
                entity.HasOne(l => l.PackError)
                    .WithMany(e => e.PackListErrors)
                    .HasForeignKey(l => l.IDPackError)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PackListError_PackError");
            });

            modelBuilder.Entity<AlertItem>(entity =>
            {
                entity.HasOne(d => d.Alert)
                    .WithMany(p => p.AlertItems)
                    .HasForeignKey(d => d.IDAlert)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AlertItem_Alert");

            });



            modelBuilder.Entity<Batch>(entity =>
            {

                entity.HasOne(e => e.BatchProduct)
                    .WithOne(e => e.Batch)
                    .HasForeignKey<BatchProduct>(e => e.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchProduct_Batch");

                entity.Property(e => e.ExpiryDate).HasColumnType("datetime");
                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");
            });



            modelBuilder.Entity<VerificationRequestItem>(entity =>
            {
                entity.HasOne(i => i.VerificationRequest)
                    .WithMany(r => r.VerificationRequestItems)
                    .HasForeignKey(i => i.IDVerificationRequest)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_VerificationRequestItem_VerificationRequest");
            });

            modelBuilder.Entity<Event>(entity =>
            {

                entity.HasOne(d => d.Batch)
                    .WithMany(p => p.Events)
                    .HasForeignKey(d => d.IDBatch)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Event_Batch");

                entity.HasOne(d => d.EventType)
                    .WithMany(p => p.Events)
                    .HasForeignKey(d => d.IDEventType)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Event_EventType");

                entity.Property(e => e.NewExpiryDate).HasColumnType("datetime");
                entity.Property(e => e.RecallEffectiveDate).HasColumnType("datetime");
                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");
                entity.Property(e => e.AutoPublishTime).HasColumnType("datetime");
            });



            modelBuilder.Entity<Market>(entity =>
            {
                entity.HasOne(d => d.Event)
                    .WithMany(p => p.Markets)
                    .HasForeignKey(d => d.IDEvent)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Market_Event");

                entity.HasOne(d => d.TargetMarket)
                    .WithMany(p => p.Markets)
                    .HasForeignKey(d => d.Code)
                    .HasPrincipalKey(p => p.ShortName)
                    .OnDelete(DeleteBehavior.ClientSetNull);
            });


            modelBuilder.Entity<PackStatusChangeRule>(entity =>
            {
                entity.HasOne(d => d.PackStatusSourceStatus)
                    .WithMany(p => p.PackStatusChangeRuleSourceStatus)
                    .HasForeignKey(d => d.IDSourceStatus)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PackStatusChangeRule_PackStatus_Source");

                entity.HasOne(d => d.PackStatusTargetStatus)
                    .WithMany(p => p.PackStatusChangeRuleTargetStatus)
                    .HasForeignKey(d => d.IDTargetStatus)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PackStatusChangeRule_PackStatus_Target");

            });



            modelBuilder.Entity<Mah>(entity =>
            {
                entity.HasOne(m => m.MahType)
                    .WithMany(m => m.Mahs)
                    .HasForeignKey(m => m.IDMahType)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Mah_MahType");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");
            });



            modelBuilder.Entity<ProductHubEventMarket>(entity =>
            {
                entity.HasOne(e => e.ProductHubEvent)
                    .WithMany(e => e.ProductHubEventMarkets)
                    .HasForeignKey(e => e.IDProductHubEvent)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductHubEventMarket_ProductHubEvent");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");
            });

            modelBuilder.Entity<EventConfirm>(entity =>
            {
                entity.HasOne(e => e.ProductHubEvent)
                    .WithMany(e => e.EventsConfirm)
                    .HasForeignKey(e => e.IDProductHubEvent)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_EventConfirm_ProductHubEvent");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampConfirm).HasColumnType("datetime");
            });

            modelBuilder.Entity<ProductError>(entity =>
            {
                entity.HasOne(e => e.ProductStatus)
                    .WithMany(e => e.ProductErrors)
                    .HasForeignKey(e => e.IDProductStatus)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductError_ProductStatus");
            });


            modelBuilder.Entity<Serial>(entity =>
            {

                entity.HasOne(d => d.Event)
                    .WithMany(p => p.Serials)
                    .HasForeignKey(d => d.IDEvent)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Serial_Event");

                entity.HasOne(d => d.PackState)
                    .WithMany(p => p.Serials)
                    .HasForeignKey(d => d.IDPackState)
                    .HasConstraintName("FK_Serial_PackState");
            });

            modelBuilder.Entity<ProductTargetMarket>(entity =>
            {
                entity.HasOne(d => d.TargetMarket)
                    .WithMany(p => p.ProductTargetMarkets)
                    .HasForeignKey(d => d.Market)
                    .HasPrincipalKey(p => p.ShortName)
                    .OnDelete(DeleteBehavior.ClientSetNull);

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");
            });

            modelBuilder.Entity<Wholesaler>((entity =>
            {
                entity.HasOne((d => d.ProductTargetMarket))
                    .WithMany(p => p.Wholesalers)
                    .HasForeignKey((d => d.IDProductTargetMarket))
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Wholesaler_TargetMarket");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");
            }));

        }
    }
}
