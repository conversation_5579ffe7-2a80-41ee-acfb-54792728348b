﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{

    [Table("HubReport")]
    public class HubReport
    {
        public HubReport()
        {
            HubReportParams = new HashSet<HubReportParam>();
            ReportRequests = new HashSet<ReportRequest>();
        }

        [Key]
        public int IDHubReport { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(100)]
        public string HubName { get; set; }

        [InverseProperty("HubReport")]
        public ICollection<HubReportParam> HubReportParams { get; set; }

        [InverseProperty("HubReport")]
        public ICollection<ReportRequest> ReportRequests { get; set; }

    }


    [Table("HubReportParam")]
    public class HubReportParam
    {
        public HubReportParam()
        {
            ReportRequestParams = new HashSet<ReportRequestParam>();
        }

        [Key]
        public int IDHubReportParam { get; set; }

        [Required]
        public int IDHubReport { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(100)]
        public string HubName { get; set; }

        [Required]
        public bool Optional { get; set; }

        [Required]
        public ParamTypeEnum ParamType { get; set; }

        public string Sql { get; set; }

        [InverseProperty("HubReportParams")]
        public HubReport HubReport { get; set; }

        [InverseProperty("HubReportParam")]
        public ICollection<ReportRequestParam> ReportRequestParams { get; set; }

    }


    [Table("ReportRequest")]
    public class ReportRequest
    {
        public ReportRequest()
        {
            ReportRequestParams = new HashSet<ReportRequestParam>();
        }

        [Key]
        public int IDReportRequest { get; set; }

        [Required]
        [DisplayName("Report type")]
        public int IDHubReport { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        public string Result { get; set; }

        [StringLength(100)]
        public string ResultFormat { get; set; }

        [StringLength(100)]
        public string ResultEncoding { get; set; }

        public string ResultError { get; set; }

        [InverseProperty("ReportRequests")]
        public HubReport HubReport { get; set; }


        [InverseProperty("ReportRequest")]
        public ICollection<ReportRequestParam> ReportRequestParams { get; set; }

        [InverseProperty("ReportRequest")]
        public ReportReady ReportReady { get; set; }

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((ReportRequest)obj).IDReportRequest == this.IDReportRequest)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }

    }

    [Table("ReportRequestParam")]
    public class ReportRequestParam
    {
        [Key]
        public int IDReportRequestParam { get; set; }

        [Required]
        public int IDReportRequest { get; set; }

        [Required]
        public int IDHubReportParam { get; set; }

        [StringLength(100)]
        public string Value { get; set; }

        [InverseProperty("ReportRequestParams")]
        public ReportRequest ReportRequest { get; set; }

        [InverseProperty("ReportRequestParams")]
        public HubReportParam HubReportParam { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((ReportRequestParam)obj).IDReportRequestParam == this.IDReportRequestParam)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }

    }



    [Table("Report")]
    public class ReportReady
    {
        [Key]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        public DateTime? Timestamp { get; set; }

        [StringLength(100)]
        public string ReportType { get; set; }

        [StringLength(100)]
        public string ReportID { get; set; }

        [StringLength(10)]
        public string ErrorCode { get; set; }

        public string ErrorDescription { get; set; }

        [InverseProperty("ReportReady")]
        public ReportRequest ReportRequest { get; set; }

    }

    public enum ParamTypeEnum
    {
        DBLIST,
        STRING,
        DBSTRING
    }
}
