﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("PackListError")]
    public class PackListError
    {
        [Key]
        public long IDPackListError { get; set; }

        [Required]
        public long IDPackError { get; set; }

        [Required]
        [StringLength(10)]
        public string Code { get; set; }

        [StringLength(20)]
        public string SerialNumber { get; set; }

        [InverseProperty("PackListErrors")]
        public PackError PackError { get; set; }

        public string GetDescription(List<PackListErrorDescription> descriptions)
        {
            try
            {
                if (descriptions != null)
                {
                    var descr = descriptions.FirstOrDefault(d => d.Code == this.Code);
                    if (descr != null)
                    {
                        return descr.Description;
                    }
                }
                return "n/a";
            }
            catch (Exception)
            {
                return string.Empty;

            }
        }
    }
}
