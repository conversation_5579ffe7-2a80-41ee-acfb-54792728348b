﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("Market")]
    public class Market
    {
        [Key]
        public int IDMarket { get; set; }

        [Required]
        public int IDEvent { get; set; }

        [Required]
        public string Code { get; set; }

        public bool Recalled { get; set; }
    }
}
