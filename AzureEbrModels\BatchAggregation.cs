﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchAggregation")]
    public class BatchAggregation
    {
        public BatchAggregation()
        {
            BatchAggregationExtends = new HashSet<BatchAggregationExtend>();
            BatchAggregationSerials = new HashSet<BatchAggregationSerial>();
            BatchSerialsRequests = new HashSet<BatchSerialsRequest>();
            LineRequests = new HashSet<LineRequest>();
            BatchTemplateLevelFiles = new HashSet<BatchTemplateLevelFile>();
        }

        [Key]
        public int IDBatchAggregation { get; set; }

        [Required]
        public int IDBatch { get; set; }

        [Required]
        [StringLength(100)]
        public string Unit { get; set; }

        [Required]
        [StringLength(20)]
        public string SerialType { get; set; }

        [Required]
        public int? UnitQuantity { get; set; }

        [Required]
        [StringLength(100)]
        public string Item { get; set; }

        [Required]
        [StringLength(20)]
        public string ItemSerialType { get; set; }

        [Required]
        public int? ItemQuantity { get; set; }

        [NotMapped]
        public List<LevelCapacityAndGTIN> ItemQuantityList { get; set; }

        [NotMapped]
        public bool DisableItemQuantity { get; set; }

        [NotMapped]
        public bool DisableUnitQuantity { get; set; }

        [NotMapped]
        public bool DisableExtensionDigit { get; set; }

        [NotMapped]
        public bool DisableCompanyPrefix { get; set; }

        [StringLength(50)]
        public string DefPoolCode { get; set; }

        public bool DefPoolRequestElevation { get; set; }

        public short? SSCCExtensionDigit { get; set; }

        [StringLength(20)]
        public string SSCCCopmanyPrefix { get; set; }

        public string DefPoolName { get; set; }

        [StringLength(14)]
        public string GTIN { get; set; }


        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        public int? IDProductLabel { get; set; }

        public int? IDLabelTemplate { get; set; }

        public int? IDTemplateLevel { get; set; }


        [StringLength(50)]
        public string SnxPoolCode { get; set; }

        [StringLength(100)]
        public string SnxPoolName { get; set; }

        public int? SnxPoolSize { get; set; }


        [InverseProperty("BatchAggregations")]
        public Batch Batch { get; set; }

        [InverseProperty("BatchAggregation")]
        public ICollection<BatchAggregationExtend> BatchAggregationExtends { get; set; }

        [InverseProperty("BatchAggregation")]
        public ICollection<BatchAggregationSerial> BatchAggregationSerials { get; set; }

        [InverseProperty("BatchAggregation")]
        public ICollection<BatchSerialsRequest> BatchSerialsRequests { get; set; }

        [InverseProperty("BatchAggregation")]
        public ICollection<LineRequest> LineRequests { get; set; }

        [InverseProperty("BatchAggregation")]
        public ICollection<BatchTemplateLevelFile> BatchTemplateLevelFiles { get; set; }


        [NotMapped]
        public List<TemplateLevelFileNoData> Files { get; set; }

        public BatchAggregation Clone()
        {
            BatchAggregation newBatch = new BatchAggregation
            {

                IDBatchAggregation = this.IDBatchAggregation,
                SSCCCopmanyPrefix = this.SSCCCopmanyPrefix,
                SSCCExtensionDigit = this.SSCCExtensionDigit,
                DefPoolCode = this.DefPoolCode,
                DefPoolName = this.DefPoolName,
                DefPoolRequestElevation = this.DefPoolRequestElevation,
                IDBatch = this.IDBatch,
                Item = this.Item,
                ItemQuantity = this.ItemQuantity,
                SerialType = this.SerialType,
                ItemSerialType = this.ItemSerialType,
                Unit = this.Unit,
                UnitQuantity = this.UnitQuantity,
                TimestampUpdated = this.TimestampUpdated,
                UserUpdated = this.UserUpdated,
                TimestampCreated = this.TimestampCreated,
                UserCreated = this.UserCreated,
                GTIN = this.GTIN,
                SnxPoolCode = this.SnxPoolCode,
                SnxPoolName = this.SnxPoolName,
                SnxPoolSize = this.SnxPoolSize,
                BatchAggregationExtends = this.BatchAggregationExtends
                    .Select(be => new BatchAggregationExtend
                    {
                        IDBatchAggregationExtend = be.IDBatchAggregationExtend,
                        IDBatchAggregation = be.IDBatchAggregation,
                        Name = be.Name,
                        GS1AICode = be.GS1AICode,
                        StringValue = be.StringValue,
                        Regex = be.Regex,
                        IsRequired = be.IsRequired,
                        IsSelectValue = be.IsSelectValue,
                        Type = be.Type,
                        IDExtendProperty = be.IDExtendProperty
                    }).ToList()
            };

            return newBatch;
        }
    }
}
