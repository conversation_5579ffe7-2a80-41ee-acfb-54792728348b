{"Version": 1, "WorkspaceRootPath": "S:\\EUHub\\Dev\\SATT_PLATFORM\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|s:\\euhub\\dev\\satt_platform\\gsnxscdatatransfer\\main.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|solutionrelative:gsnxscdatatransfer\\main.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|s:\\euhub\\dev\\satt_platform\\gsnxscdatatransfer\\scripts\\25. updateandgetcustomerserialsbatch.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|solutionrelative:gsnxscdatatransfer\\scripts\\25. updateandgetcustomerserialsbatch.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|s:\\euhub\\dev\\satt_platform\\gsnxscdatatransfer\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|solutionrelative:gsnxscdatatransfer\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|s:\\euhub\\dev\\satt_platform\\gsnxscdatatransfer\\gsnxapiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|solutionrelative:gsnxscdatatransfer\\gsnxapiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|s:\\euhub\\dev\\satt_platform\\gsnxscdatatransfer\\custommodels\\customerproduct.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|solutionrelative:gsnxscdatatransfer\\custommodels\\customerproduct.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0EEBB517-18A3-4BCD-9D3E-285506C5D68B}|GSNXApi\\GSNXApi.csproj|s:\\euhub\\dev\\satt_platform\\gsnxapi\\controllers\\apiv1controller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0EEBB517-18A3-4BCD-9D3E-285506C5D68B}|GSNXApi\\GSNXApi.csproj|solutionrelative:gsnxapi\\controllers\\apiv1controller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|s:\\euhub\\dev\\satt_platform\\gsnxscdatatransfer\\scripts\\22. getcustomerserials.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|solutionrelative:gsnxscdatatransfer\\scripts\\22. getcustomerserials.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|s:\\euhub\\dev\\satt_platform\\gsnxscdatatransfer\\scripts\\21. getcustomerproducts.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|solutionrelative:gsnxscdatatransfer\\scripts\\21. getcustomerproducts.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|s:\\euhub\\dev\\satt_platform\\gsnxscdatatransfer\\scripts\\11. createtable-cipiterationuploadstate.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|solutionrelative:gsnxscdatatransfer\\scripts\\11. createtable-cipiterationuploadstate.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{1474200F-9B38-4BDF-9DC5-C9CC4A00859A}|AzureSnxModels\\AzureSnxModels.csproj|s:\\euhub\\dev\\satt_platform\\azuresnxmodels\\requestparameters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1474200F-9B38-4BDF-9DC5-C9CC4A00859A}|AzureSnxModels\\AzureSnxModels.csproj|solutionrelative:azuresnxmodels\\requestparameters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|S:\\SATT\\Dev\\Connector\\sgLSMEBR\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{773E1378-EB5C-4D87-BE97-CA7C6E510CFA}|SATTAmHubGateway\\SATTAmHubGateway.csproj|s:\\euhub\\dev\\satt_platform\\sattamhubgateway\\pharmaconnector\\json\\order.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{773E1378-EB5C-4D87-BE97-CA7C6E510CFA}|SATTAmHubGateway\\SATTAmHubGateway.csproj|solutionrelative:sattamhubgateway\\pharmaconnector\\json\\order.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8D3C714E-20AC-495E-A2F4-B5D09BDA7BB0}|RazorProductCreate\\RazorProductCreate.csproj|s:\\euhub\\dev\\satt_platform\\razorproductcreate\\productcreate.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{8D3C714E-20AC-495E-A2F4-B5D09BDA7BB0}|RazorProductCreate\\RazorProductCreate.csproj|solutionrelative:razorproductcreate\\productcreate.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DF1E319C-69B6-43BF-8042-B2EDA3DA0A53}|SATTTRVST\\SATTTRVST.csproj|s:\\euhub\\dev\\satt_platform\\satttrvst\\globalvariables.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DF1E319C-69B6-43BF-8042-B2EDA3DA0A53}|SATTTRVST\\SATTTRVST.csproj|solutionrelative:satttrvst\\globalvariables.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DF1E319C-69B6-43BF-8042-B2EDA3DA0A53}|SATTTRVST\\SATTTRVST.csproj|s:\\euhub\\dev\\satt_platform\\satttrvst\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DF1E319C-69B6-43BF-8042-B2EDA3DA0A53}|SATTTRVST\\SATTTRVST.csproj|solutionrelative:satttrvst\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{CE9DB762-7DD1-40A9-9DB8-28DF54B0EE68}|TRVSTApi\\TRVSTApi.csproj|s:\\euhub\\dev\\satt_platform\\trvstapi\\trvstapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CE9DB762-7DD1-40A9-9DB8-28DF54B0EE68}|TRVSTApi\\TRVSTApi.csproj|solutionrelative:trvstapi\\trvstapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CE9DB762-7DD1-40A9-9DB8-28DF54B0EE68}|TRVSTApi\\TRVSTApi.csproj|s:\\euhub\\dev\\satt_platform\\trvstapi\\trvstapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{CE9DB762-7DD1-40A9-9DB8-28DF54B0EE68}|TRVSTApi\\TRVSTApi.csproj|solutionrelative:trvstapi\\trvstapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|S:\\EUHub\\Dev\\SATT_PLATFORM\\EbrTest\\Pages\\Error.cshtml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:EbrTest\\Pages\\Error.cshtml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|S:\\EUHub\\Dev\\SATT_PLATFORM\\AS2Api\\Controllers\\ProxyController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:AS2Api\\Controllers\\ProxyController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|s:\\euhub\\dev\\satt_platform\\gsnxscdatatransfer\\scripts\\25. updateandgetcustomerserialsbatch.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|CodeFrame", "RelativeMoniker": "D:0:0:{705E2650-D869-435C-848F-7EA427F4F921}|GSNXSCDataTransfer\\GSNXSCDataTransfer.csproj|solutionrelative:gsnxscdatatransfer\\scripts\\25. updateandgetcustomerserialsbatch.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|CodeFrame"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 25, "Children": [{"$type": "Bookmark", "Name": "ST:16:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:20:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:21:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{80454082-9ab8-47d4-af23-82bf6739e2a9}"}, {"$type": "Bookmark", "Name": "ST:23:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:24:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:129:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:133:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:0:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Bookmark", "Name": "ST:17:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:18:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{dcc4ea97-1c0c-482b-b205-e541c0df9728}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:14:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:12:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:13:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:11:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "CustomerProduct.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\CustomModels\\CustomerProduct.cs", "RelativeDocumentMoniker": "GSNXSCDataTransfer\\CustomModels\\CustomerProduct.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\CustomModels\\CustomerProduct.cs [Read Only]", "RelativeToolTip": "GSNXSCDataTransfer\\CustomModels\\CustomerProduct.cs [Read Only]", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T13:19:18.187Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 3, "Title": "GsnxApiClient.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\GsnxApiClient.cs", "RelativeDocumentMoniker": "GSNXSCDataTransfer\\GsnxApiClient.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\GsnxApiClient.cs", "RelativeToolTip": "GSNXSCDataTransfer\\GsnxApiClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEgAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T13:06:23.496Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Program.cs", "RelativeDocumentMoniker": "GSNXSCDataTransfer\\Program.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Program.cs", "RelativeToolTip": "GSNXSCDataTransfer\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T09:14:09.193Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Main.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Main.cs", "RelativeDocumentMoniker": "GSNXSCDataTransfer\\Main.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Main.cs*", "RelativeToolTip": "GSNXSCDataTransfer\\Main.cs*", "ViewState": "AgIAAHsCAAAAAAAAAAAqwLYCAAByAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T09:06:09.612Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ApiV1Controller.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXApi\\Controllers\\ApiV1Controller.cs", "RelativeDocumentMoniker": "GSNXApi\\Controllers\\ApiV1Controller.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXApi\\Controllers\\ApiV1Controller.cs", "RelativeToolTip": "GSNXApi\\Controllers\\ApiV1Controller.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACgAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T08:52:33.99Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "appsettings.json", "DocumentMoniker": "S:\\SATT\\Dev\\Connector\\sgLSMEBR\\appsettings.json", "RelativeDocumentMoniker": "..\\..\\..\\SATT\\Dev\\Connector\\sgLSMEBR\\appsettings.json", "ToolTip": "S:\\SATT\\Dev\\Connector\\sgLSMEBR\\appsettings.json", "RelativeToolTip": "..\\..\\..\\SATT\\Dev\\Connector\\sgLSMEBR\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-04-01T12:02:26.88Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "Order.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\SATTAmHubGateway\\PharmaConnector\\Json\\Order.cs", "RelativeDocumentMoniker": "SATTAmHubGateway\\PharmaConnector\\Json\\Order.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\SATTAmHubGateway\\PharmaConnector\\Json\\Order.cs", "RelativeToolTip": "SATTAmHubGateway\\PharmaConnector\\Json\\Order.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T08:51:29.565Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "Error.cshtml.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\EbrTest\\Pages\\Error.cshtml.cs", "RelativeDocumentMoniker": "EbrTest\\Pages\\Error.cshtml.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\EbrTest\\Pages\\Error.cshtml.cs [Read Only]", "RelativeToolTip": "EbrTest\\Pages\\Error.cshtml.cs [Read Only]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-20T08:15:49.012Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 18, "Title": "ProxyController.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\AS2Api\\Controllers\\ProxyController.cs", "RelativeDocumentMoniker": "AS2Api\\Controllers\\ProxyController.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\AS2Api\\Controllers\\ProxyController.cs", "RelativeToolTip": "AS2Api\\Controllers\\ProxyController.cs", "ViewState": "AgIAADIAAAAAAAAAAAAMwE0AAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-20T08:15:47.731Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ProductCreate.razor", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\RazorProductCreate\\ProductCreate.razor", "RelativeDocumentMoniker": "RazorProductCreate\\ProductCreate.razor", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\RazorProductCreate\\ProductCreate.razor", "RelativeToolTip": "RazorProductCreate\\ProductCreate.razor", "ViewState": "AgIAAKkCAAAAAAAAAAAewMYCAABYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-03-19T15:17:58.137Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "appsettings.json", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\SATTTRVST\\appsettings.json", "RelativeDocumentMoniker": "SATTTRVST\\appsettings.json", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\SATTTRVST\\appsettings.json", "RelativeToolTip": "SATTTRVST\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-04T12:13:26.791Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "GlobalVariables.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\SATTTRVST\\GlobalVariables.cs", "RelativeDocumentMoniker": "SATTTRVST\\GlobalVariables.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\SATTTRVST\\GlobalVariables.cs", "RelativeToolTip": "SATTTRVST\\GlobalVariables.cs", "ViewState": "AgIAALQAAAAAAAAAAAAQwN4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-04T12:09:53.338Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "11. CreateTable-CIPIterationUploadState.sql", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Scripts\\11. CreateTable-CIPIterationUploadState.sql", "RelativeDocumentMoniker": "GSNXSCDataTransfer\\Scripts\\11. CreateTable-CIPIterationUploadState.sql", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Scripts\\11. CreateTable-CIPIterationUploadState.sql", "RelativeToolTip": "GSNXSCDataTransfer\\Scripts\\11. CreateTable-CIPIterationUploadState.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-07T15:00:32.145Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "25. UpdateAndGetCustomerSerialsBatch.sql ", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Scripts\\25. UpdateAndGetCustomerSerialsBatch.sql", "RelativeDocumentMoniker": "GSNXSCDataTransfer\\Scripts\\25. UpdateAndGetCustomerSerialsBatch.sql", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Scripts\\25. UpdateAndGetCustomerSerialsBatch.sql", "RelativeToolTip": "GSNXSCDataTransfer\\Scripts\\25. UpdateAndGetCustomerSerialsBatch.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-07T14:34:07.45Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "21. GetCustomerProducts.sql", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Scripts\\21. GetCustomerProducts.sql", "RelativeDocumentMoniker": "GSNXSCDataTransfer\\Scripts\\21. GetCustomerProducts.sql", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Scripts\\21. GetCustomerProducts.sql", "RelativeToolTip": "GSNXSCDataTransfer\\Scripts\\21. GetCustomerProducts.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-07T14:31:09.52Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "22. GetCustomerSerials.sql", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Scripts\\22. GetCustomerSerials.sql", "RelativeDocumentMoniker": "GSNXSCDataTransfer\\Scripts\\22. GetCustomerSerials.sql", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\GSNXSCDataTransfer\\Scripts\\22. GetCustomerSerials.sql", "RelativeToolTip": "GSNXSCDataTransfer\\Scripts\\22. GetCustomerSerials.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-07T14:32:12.127Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "TRVSTApi.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\TRVSTApi\\TRVSTApi.cs", "RelativeDocumentMoniker": "TRVSTApi\\TRVSTApi.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\TRVSTApi\\TRVSTApi.cs", "RelativeToolTip": "TRVSTApi\\TRVSTApi.cs", "ViewState": "AQIAAAwAAAAAAAAAAAAAACEAAAAJAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-04T12:09:38.452Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "TRVSTApi.csproj", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\TRVSTApi\\TRVSTApi.csproj", "RelativeDocumentMoniker": "TRVSTApi\\TRVSTApi.csproj", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\TRVSTApi\\TRVSTApi.csproj [Read Only]", "RelativeToolTip": "TRVSTApi\\TRVSTApi.csproj [Read Only]", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-12-04T12:09:32.648Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 9, "Title": "RequestParameters.cs", "DocumentMoniker": "S:\\EUHub\\Dev\\SATT_PLATFORM\\AzureSnxModels\\RequestParameters.cs", "RelativeDocumentMoniker": "AzureSnxModels\\RequestParameters.cs", "ToolTip": "S:\\EUHub\\Dev\\SATT_PLATFORM\\AzureSnxModels\\RequestParameters.cs", "RelativeToolTip": "AzureSnxModels\\RequestParameters.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T10:24:52.746Z"}, {"$type": "Bookmark", "Name": "ST:0:0:{99b8fa2f-ab90-4f57-9c32-949f146f1914}"}]}]}]}