﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("ProductWithdrawalMarket")]
    public class ProductWithdrawalMarket
    {
        [Key]
        public int IDProductWithdrawalMarket { get; set; }

        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? Timestamp { get; set; }

        [StringLength(2)]
        public string Market { get; set; }

        public bool Withdraw { get; set; }

    }
}
