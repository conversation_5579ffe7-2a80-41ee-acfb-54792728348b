﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("AppLog")]
    public class AppLog
    {
        [Key]
        public int IDAppLog { get; set; }

        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [Required]
        public DateTime TimestampCreated { get; set; }
    }
}
