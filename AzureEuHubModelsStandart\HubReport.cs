﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("HubReport")]
    public class HubReport
    {
        [Key]
        public int IDHubReport { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(100)]
        public string HubName { get; set; }
        
    }


    [Table("HubReportParam")]
    public class HubReportParam
    {
     

        [Key]
        public int IDHubReportParam { get; set; }

        [Required]
        public int IDHubReport { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(100)]
        public string HubName { get; set; }

        [Required]
        public bool Optional { get; set; }

        [Required]
        public ParamTypeEnum ParamType { get; set; }

        public string Sql { get; set; }

    }


    [Table("ReportRequest")]
    public class ReportRequest
    {

        [Key]
        public int IDReportRequest { get; set; }

        [Required]
        public int IDHubReport { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        public string Result { get; set; }

        [StringLength(100)]
        public string ResultFormat { get; set; }

        [StringLength(100)]
        public string ResultEncoding { get; set; }

        public string ResultError { get; set; }

    }

    [Table("ReportRequestParam")]
    public class ReportRequestParam
    {
        [Key]
        public int IDReportRequestParam { get; set; }

        [Required]
        public int IDReportRequest { get; set; }

        [Required]
        public int IDHubReportParam { get; set; }

        [StringLength(100)]
        public string Value { get; set; }


    }



    [Table("Report")]
    public class ReportReady
    {
        [Key]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        public DateTime? Timestamp { get; set; }

        [StringLength(100)]
        public string ReportType { get; set; }

        [StringLength(100)]
        public string ReportID { get; set; }

        [StringLength(10)]
        public string ErrorCode { get; set; }

        public string ErrorDescription { get; set; }


    }

    public enum ParamTypeEnum
    {
        DBLIST,
        STRING,
        DBSTRING
    }
}
