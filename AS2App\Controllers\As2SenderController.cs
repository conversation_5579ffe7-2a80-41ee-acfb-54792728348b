using AS2Test.Features.Services;  // Changed to match interface file
using AS2Test_master.Models;
using Microsoft.AspNetCore.Mvc;
using AS2Test.Models;
using System.Text;
using AS2App.Features.Services.AzureLogStorageService;

namespace AS2App.Controllers
{
    [ApiController]
    [Produces("application/json")]
    [Route("api/[controller]")]
#if DEBUG
    //[Authorize] 
#else
    [Authorize(Roles = ADGroups.WebApi)]
#endif

    // [Authorize(AuthenticationSchemes = "Internal")] todo return
    /// <summary>
    /// Controller for handling AS2 fire operations.
    /// </summary>
    public class As2senderController : ControllerBase  
    {
        private readonly ILogger<As2senderController> _logger;
        private readonly IAs2MessageSenderService _as2MessageSenderService;  
        private readonly HttpClient _httpClient;
        private readonly AzureAs2ProxyModels.DBModel _azureAs2ProxyDBModel;
        private readonly IAzureLogStorageService _azureLogStorageService;


        public As2senderController(
            ILogger<As2senderController> logger,
            HttpClient httpClient,
            IAs2MessageSenderService as2MessageSenderService,
            AzureAs2ProxyModels.DBModel azureAs2ProxyDBModel,
            IAzureLogStorageService azureLogStorageService)  
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _as2MessageSenderService = as2MessageSenderService ?? throw new ArgumentNullException(nameof(as2MessageSenderService));
            _azureLogStorageService = azureLogStorageService ?? throw new ArgumentNullException(nameof(azureLogStorageService));
            _azureAs2ProxyDBModel = azureAs2ProxyDBModel ?? throw new ArgumentNullException(nameof(azureAs2ProxyDBModel));

        }

        public AzureAs2ProxyModels.DBModel AzureAs2ProxyDBModel
        {
            get
            {
                if (_azureAs2ProxyDBModel != null)
                    return _azureAs2ProxyDBModel;

                else
                    throw new Exception("Error! SATT PLATFORM [AS2Proxy] Database is not connected");
            }
        }

        /// <summary>
        /// Handles the HTTP POST request for sending a transaction message.
        /// </summary>
        /// <param name="messageRequest">The message request containing the transaction data.</param>
        /// <returns>An action result containing the service response with the HTTP response message.</returns>
        /// <remarks>
        /// Performs a transformation of the provided data to an AS2 specific MIME Message and sends via HTTP 
        /// </remarks>
        [HttpPost]
        [ProducesResponseType(typeof(ServiceResponse<As2ServerResponse<HttpResponseMessage>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ServiceResponse<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ServiceResponse<As2ServerResponse<HttpResponseMessage>>>> PostTransaction([FromBody] MessageRequest messageRequest)
        {
            // Validate MDN requirements
            if (messageRequest.MdnOption.IsMdnRequired &&
                string.IsNullOrEmpty(messageRequest.MdnOption.MessageIntegrityCheckAlgorithm.ToString()))
            {
                ModelState.AddModelError(
                    nameof(messageRequest.MdnOption.MessageIntegrityCheckAlgorithm),
                    "Message Integrity Check algorithm is mandatory when MDN is required!");
            }

            try
            {
                _logger.LogInformation("Sending AS2 message");

                SaveRequestToDB(messageRequest);
                _azureLogStorageService.LogObjectToStorage(messageRequest, "MessageRequest", messageRequest.MessageId);


                var sendResult = await _as2MessageSenderService.SendFileAsync(messageRequest);

                UpdateRequest(sendResult, messageRequest);

                _azureLogStorageService.LogObjectToStorage(sendResult, "RequestResponse", sendResult.As2ServerResponse.Message.OriginalMessageId);



                if (sendResult.IsSuccess)
                {
                    _logger.LogInformation("AS2 message sent successfully");
                    return Ok(sendResult);
                }


                _logger.LogWarning("Failed to send AS2 message: {ErrorMessage}", sendResult.ErrorMessage);
                return BadRequest(sendResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while sending AS2 message");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ServiceResponse<object>
                    {
                        IsSuccess = false,
                        ErrorMessage = "An unexpected error occurred while processing the request."
                    });
            }
        }

        private void UpdateRequest(ServiceResponse<As2ServerResponse<MdnVerificationResult>> sendResult, MessageRequest messageRequest)
        {
            Guid messageGUID;

            var message = sendResult.As2ServerResponse?.Message;

            if (message != null && !string.IsNullOrEmpty(message.OriginalMessageId))
            {
                messageGUID = Guid.Parse(message.OriginalMessageId.Replace("<", "").Replace(">", ""));
            }
            else
            {
                messageGUID = Guid.Parse(messageRequest.MessageId);
            }

            var request = AzureAs2ProxyDBModel
                .Requests
                .FirstOrDefault(r => r.MessageId == messageGUID);

            if (request == null)
            {
                var missingId = message != null ? message.OriginalMessageId : messageRequest.MessageId;
                throw new Exception($"Attempted to update Request State. Request with MessageId {missingId} not found in database.");
            }

            request.ErrorMessage = sendResult.ErrorMessage ?? message?.Error;
            request.ResultText = message?.MDNTextPart;
            request.IsMicValid = message?.IsMicValid ?? false;
            request.IsSignatureValid = message?.IsSignatureValid ?? false;

            if (!string.IsNullOrEmpty(message?.Error))
            {
                request.State = "Error"; // Set state to Error if there is an error message
            }
            else
            {
                request.State = message?.Disposition;
            }

            request.StateModifier = message?.DispositionModifier;
            request.TimestampUpdated = DateTime.UtcNow;

            try
            {
                AzureAs2ProxyDBModel.Requests.Update(request);
                AzureAs2ProxyDBModel.SaveChanges();
                _logger.LogInformation("Request saved to database with ID: {MessageId}", request.MessageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save request to database");

                AzureAs2ProxyDBModel.ChangeTracker.Clear();

                throw;
            }
        }

        private void SaveRequestToDB(MessageRequest messageRequest)
        {
            var newRequest = new AzureAs2ProxyModels.Request()
            {
                MessageId = Guid.NewGuid(),
                IDCustomer = messageRequest.IDCustomer.Value,
                IDReport = messageRequest.IDReport.Value,
                UrlEndpoint = messageRequest.ServerUri.ToString(),
                State = "New", // Initial state
                StateModifier = null,
                ErrorMessage = null,
                ResultText = null,
                IsMicValid = null,
                IsSignatureValid = null,
                TimestampCreated = DateTime.UtcNow,
            };

            messageRequest.MessageId = newRequest.MessageId.ToString();

            AzureAs2ProxyDBModel.Requests.Add(newRequest);

            try
            {
                AzureAs2ProxyDBModel.SaveChanges();
                _logger.LogInformation("Request saved to database with ID: {MessageId}", newRequest.MessageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save request to database");

                AzureAs2ProxyDBModel.ChangeTracker.Clear();

                throw; // Re-throw the exception to be handled by the caller
            }

        }
        /// <summary>
        /// Retrieves the server status by sending a POST request to the specified URI.
        /// </summary>
        /// <returns>An ActionResult containing a ServiceResponse object with the server status.</returns>
        [HttpPost("status")]
        [ProducesResponseType(typeof(ServiceResponse<As2ServerResponse<string>>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ServiceResponse<As2ServerResponse<string>>>> GetServerStatus(string serverUrl)
        {
            try
            {
                _logger.LogInformation("Checking AS2 server status at {Url}", serverUrl);
                var response = await _httpClient.PostAsync(new Uri(serverUrl), null);

                if (response.IsSuccessStatusCode)
                {
                    string content = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("AS2 server status check successful");

                    return Ok(new ServiceResponse<As2ServerResponse<string>>
                    {
                        IsSuccess = true,
                        As2ServerResponse = new As2ServerResponse<string>
                        {
                            Message = content,
                            StatusCode = response.StatusCode.ToString()
                        }
                    });
                }

                _logger.LogWarning("AS2 server status check failed with status code: {StatusCode}", response.StatusCode);
                return BadRequest(new ServiceResponse<As2ServerResponse<string>>
                {
                    IsSuccess = false,
                    ErrorMessage = $"Server returned status code: {response.StatusCode}",
                    As2ServerResponse = new As2ServerResponse<string>
                    {
                        StatusCode = response.StatusCode.ToString()
                    }
                });
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP request exception during AS2 server status check");
                return StatusCode(StatusCodes.Status500InternalServerError, CreateErrorResponse("Connection error: " + ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "Invalid operation exception during AS2 server status check");
                return StatusCode(StatusCodes.Status500InternalServerError, CreateErrorResponse("Operation error: " + ex.Message));
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogError(ex, "Task canceled exception during AS2 server status check");
                return StatusCode(StatusCodes.Status500InternalServerError, CreateErrorResponse("Request timeout: " + ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected exception during AS2 server status check");
                return StatusCode(StatusCodes.Status500InternalServerError, CreateErrorResponse("Unexpected error: " + ex.Message));
            }
        }

        [HttpPost("receive")]
        public async Task<IActionResult> ReceiveMessage()
        {
            _logger.LogInformation("=== Incoming AS2 Request ===");

            // Log HTTP headers
            foreach (var header in Request.Headers)
            {
                _logger.LogInformation("{Header}: {Value}", header.Key, header.Value);
            }

            // Read and log the raw body
            using var reader = new StreamReader(Request.Body, Encoding.UTF8);
            var body = await reader.ReadToEndAsync();

            _logger.LogInformation("=== Raw Body Start ===");
            _logger.LogInformation(body);
            _logger.LogInformation("=== Raw Body End ===");

            // You can return a minimal MDN if needed here later
            return Ok("AS2 message received");
        }

        private ServiceResponse<As2ServerResponse<string>> CreateErrorResponse(string errorMessage)
        {
            return new ServiceResponse<As2ServerResponse<string>>
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                As2ServerResponse = new As2ServerResponse<string>
                {
                    StatusCode = "Error"
                }
            };
        }
    }
}