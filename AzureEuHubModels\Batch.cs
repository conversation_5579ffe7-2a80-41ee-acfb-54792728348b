﻿using SgXmlEvent;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{

    [Table("Batch")]
    public class Batch
    {
        public Batch()
        {
            Events = new HashSet<Event>();
        }

        public Batch(int idProduct, string batchID, DateTime expiryDate, string lgnName)
        {
            this.IDProduct = idProduct;
            this.BatchID = batchID;
            this.ExpiryDate = expiryDate;
            this.UserCreated = lgnName;
            this.TimestampCreated = DateTime.Now;

            this.Events = new HashSet<Event>();
        }

        public Batch(SgXmlEvent.SgXmlEvent file, int idProduct, string lgnName)
        {
            this.BatchID = file.LOT;
            this.ExpiryDate = file.EXP.Value;
            this.ProductCode = file.CIP;
            this.ProductCodeType = "GTIN";
            this.IDProduct = idProduct;
            this.TimestampCreated = DateTime.Now;
            this.UserCreated = lgnName;

            this.Events = new HashSet<Event>();
        }

        public Batch(string productCode, string batchID, DateTime expiryDate, bool isExpiryZeroDay, string lgnName, int? idProduct)
        {
            this.ProductCode = productCode;
            this.ProductCodeType = "GTIN";
            this.BatchID = batchID;
            this.ExpiryDate = expiryDate;
            this.UserCreated = lgnName;
            this.TimestampCreated = DateTime.Now;
            this.IsExpiryDateZeroDay = isExpiryZeroDay;
            this.IDProduct = idProduct;
        }

        public override bool Equals(object obj)
        {
            if(obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((Batch)obj).IDBatch == this.IDBatch)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

        [Key]
        public int IDBatch { get; set; }

        public int? IDProduct { get; set; }

        [Required]
        [StringLength(10)]
        public string ProductCodeType { get; set; }

        [Required]
        [StringLength(14)]
        public string ProductCode { get; set; }

        [Required]
        [StringLength(20)]
        [RegularExpression(@"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{1,20})$",
        ErrorMessage = "Batch ID is expected to be alphanumeric values of up to 20 characters, limited to those characters defined in the GS1 General  Specifications.")]
        public string BatchID { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? ExpiryDate { get; set; }

        public int PackSize { get; set; }

        [StringLength(10)]
        public string OriginalProductCodeType { get; set; }

        [CodeValidationAllowEmpty(ErrorMessage = "Code is not valid GTIN or PPN number")]
        [StringLength(14)]
        public string OriginalProductCode { get; set; }

        [StringLength(20)]
        [RegularExpression(@"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{1,20})$",
        ErrorMessage = "Batch ID is expected to be alphanumeric values of up to 20 characters, limited to those characters defined in the GS1 General  Specifications.")]
        public string OriginalProductBatchID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? OriginalProductExpiryDate { get; set; }

        public bool IsOriginalProductExpiryDateZeroDay { get; set; }

        [StringLength(50)]
        public string MahID { get; set; }

        [StringLength(100)]
        public string MahName { get; set; }

        [StringLength(255)]
        public string MahStreet { get; set; }

        [StringLength(255)]
        public string MahStreet2 { get; set; }

        [StringLength(100)]
        public string MahCity { get; set; }

        [StringLength(50)]
        public string MahPostCode { get; set; }

        public string MahCountryCode { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        public bool Locked { get; set; }

        public bool IsExpiryDateZeroDay { get; set; }

        public bool SupportRequest { get; set; }

        public bool MaintenanceMode { get; set; }

        [InverseProperty("Batch")]
        public ICollection<Event> Events { get; set; }

        [InverseProperty("Batch")]
        public BatchProduct BatchProduct { get; set; }
    }
}
