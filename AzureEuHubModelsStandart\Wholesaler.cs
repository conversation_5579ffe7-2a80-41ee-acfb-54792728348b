﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("Wholesaler")]
    public class Wholesaler
    {
        [Key]
        public int IDWholesaler { get; set; }

        public int IDProductTargetMarket { get; set; }

        [StringLength(50)]
        public string WholesalerID { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(255)]
        public string Street { get; set; }

        [StringLength(255)]
        public string Street2 { get; set; }

        [StringLength(255)]
        public string City { get; set; }

        [StringLength(50)]
        public string PostCode { get; set; }

        [StringLength(2)]
        public string CountryCode { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }
    }
}
