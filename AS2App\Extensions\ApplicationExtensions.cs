using AS2Test.Features.Services;
using AS2Test.Features.Services.MdnHandler;
using AS2Test.Features.Services.MessageCreator;
using AS2Test.Features.Services.MimePayload;
using AS2Test_master.Features.Services.Certificates;
using AS2Test_master.Features.Services.MimePayload;
using AS2App.Features.Services;
using Microsoft.Extensions.Options;
using AS2App.Models.ConfigModels;
using AS2App.Features.Services.MicHandler;
using AS2App.Features.Services.AzureLogStorageService;

namespace AS2Test_master.Extensions
{

    public static class ApplicationExtensions
    {
        /// <summary>
        /// Adds application services to the specified <see cref="IServiceCollection"/>.
        /// </summary>
        /// <param name="services">The <see cref="IServiceCollection"/> to add the services to.</param>
        /// <param name="config">The <see cref="IConfiguration"/> containing the application settings.</param>
        /// <returns>The modified <see cref="IServiceCollection"/>.</returns>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services, IConfiguration config)
        {
            IServiceProvider serviceProvider = services.BuildServiceProvider();
            
            services.AddScoped<ICertificateService, CertificateService>();
            services.AddScoped<IMdnDecryptionVerificationService, MdnDecryptionVerificationService>();
            services.AddScoped<IMimePayloadService, MimePayloadService>();
            services.AddScoped<IMimeMessagePayloadService, MimeMessagePayloadService>();
            services.AddScoped<ICertificateService,CertificateService>();
            services.AddScoped<IMessageCreatorService, MessageCreatorService>();
            services.AddScoped<IMicHandlerService, MicHandlerService>();
            services.AddScoped<IAzureLogStorageService, AzureLogStorageService>();


            services.AddHttpClient<IAs2MessageSenderService, As2MessageSenderService>((serviceProvider, c) =>
            {
                c.Timeout = TimeSpan.FromMinutes(15);
            });
            services.AddMemoryCache();            
             
            return services;
        }
    }
}