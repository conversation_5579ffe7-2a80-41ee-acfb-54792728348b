﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("Event")]
    public class Event
    {
        public Event()
        {
            Serials = new HashSet<Serial>();
            Markets = new HashSet<Market>();
        }

        [Key]
        public int IDEvent { get; set; }

        public int IDBatch { get; set; }

        public int IDEventType { get; set; }

        [Required]
        [StringLength(50)]
        public string HubCorrelationID { get; set; }

        [StringLength(20)]
        [RegularExpression(@"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{0,20})$",
         ErrorMessage = "Batch ID is expected to be alphanumeric values of up to 20 characters, limited to those characters defined in the GS1 General  Specifications.")]
        public string NewBatchID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? NewExpiryDate { get; set; }

        public bool? NewExpDateYYMM { get; set; }

        [StringLength(50)]
        public string DistributionState { get; set; }

        public int? SourceState { get; set; }

        public int? TargetState { get; set; }

        [StringLength(512)]
        public string RecallInformation { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? RecallEffectiveDate { get; set; }

        public bool Info { get; set; }
        public bool Warrning { get; set; }
        public bool Error { get; set; }
        public bool Canceled { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        public int? MasterIDEvent { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? AutoPublishTime { get; set; }

        [InverseProperty("Events")]
        public Batch Batch { get; set; }

        [InverseProperty("Events")]
        public EventType EventType { get; set; }

        [InverseProperty("Event")]
        public ICollection<Serial> Serials { get; set; }

        [InverseProperty("Event")]
        public ICollection<Market> Markets { get; set; }

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((Event)obj).IDEvent == this.IDEvent)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }
    }
}
