﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("AlertItem")]
    public class AlertItem
    {
        [Key]
        public int IDAlertItem { get; set; }

        public int IDAlert { get; set; }

        public string Key { get; set; }

        public string Value { get; set; }

        [InverseProperty("AlertItems")]
        public Alert Alert { get; set; }

    }
}
