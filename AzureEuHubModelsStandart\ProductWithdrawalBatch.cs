﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("ProductWithdrawalBatch")]
    public class ProductWithdrawalBatch
    {
        [Key]
        public int IDProductWithdrawalBatch { get; set; }

        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? Timestamp { get; set; }

        [StringLength(50)]
        public string BatchNumber { get; set; }

        [StringLength(50)]
        public string PackCount { get; set; }
    }
}
