--------------START CMD RouterConfig table 28-05-2025 Joro, Eli--------------------------

CREATE TABLE [dbo].[RouterConfig](
	[IDRouterConfig] [int] IDENTITY(1,1) NOT NULL,
	[IsActive] [bit] NOT NULL,
	[IDCustomer] [int] NOT NULL,
	[ElementName] [nvarchar](255) NULL,
	[ProcessorType] [int] NOT NULL,
	[SourceFolderAccess] [int] NOT NULL,
	[SourceUrl] [nvarchar](255) NULL,
	[SourcePort] [int] NULL,
	[SourceUser] [nvarchar](128) NULL,
	[SourcePassword] [nvarchar](128) NULL,
	[SourceFolder] [nvarchar](255) NULL,
	[TargetFolderAccess] [int] NOT NULL,
	[TargetUrl] [nvarchar](255) NULL,
	[TargetPort] [int] NULL,
	[TargetUser] [nvarchar](128) NULL,
	[TargetPassword] [nvarchar](128) NULL,
	[TargetFolder] [nvarchar](255) NOT NULL,
	[AlternateTargetFolder] [nvarchar](255) NULL,
	[IsFileArchived] [bit] NOT NULL,
	[FileDelay] [int] NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
	[TimestampUpdated] [datetime] NULL,
	[UserUpdated] [nvarchar](128) NULL,
 CONSTRAINT [PK_RouterConfig] PRIMARY KEY CLUSTERED 
(
	[IDRouterConfig] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RouterConfig] ADD  CONSTRAINT [DF_RouterConfig_TimestampCreated]  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[RouterConfig] ADD  CONSTRAINT [DF_RouterConfig_UserCreated]  DEFAULT (suser_sname()) FOR [UserCreated]
GO



CREATE TABLE [dbo].[RouterElement](
	[IDRouterElement] [int] IDENTITY(1,1) NOT NULL,
	[IDRouterConfig] [int] NOT NULL,
	[Value] [nvarchar](255) NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
	[TimestampUpdated] [datetime] NULL,
	[UserUpdated] [nvarchar](128) NULL,
 CONSTRAINT [PK_RouterElement] PRIMARY KEY CLUSTERED 
(
	[IDRouterElement] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RouterElement]  WITH CHECK ADD  CONSTRAINT [FK_RouterElement_RouterConfig] FOREIGN KEY([IDRouterConfig])
REFERENCES [dbo].[RouterConfig] ([IDRouterConfig])
GO

ALTER TABLE [dbo].[RouterElement] CHECK CONSTRAINT [FK_RouterElement_RouterConfig]
GO
--------------END CMD RouterConfig table 28-05-2025 Joro, Eli--------------------------


--------------START CMD FileWatcherConfig table 29-05-2025 Joro, Eli--------------------------

CREATE TABLE [dbo].[FileWatcherConfig](
	[IDFileWatcherConfig] [int] IDENTITY(1,1) NOT NULL,
	[FolderID] [nvarchar](128) NOT NULL,
	[FolderEnabled] [bit] NOT NULL,
	[FolderDescription] [nvarchar](255) NULL,
	[FolderFilter] [nvarchar](128) NULL,
	[FolderPath] [nvarchar](255) NULL,
	[FolderIncludeSub] [bit] NOT NULL,
	[ExecutableFile] [nvarchar](255) NOT NULL,
	[ExecutableArguments] [nvarchar](500) NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NULL,
	[TimestampUpdated] [datetime] NULL,
	[UserUpdated] [nvarchar](128) NULL,
 CONSTRAINT [PK_FileWatcherConfig] PRIMARY KEY CLUSTERED 
(
	[IDFileWatcherConfig] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[FileWatcherConfig] ADD  CONSTRAINT [DF_FileWatcherConfig_TimestampCreated]  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[FileWatcherConfig] ADD  CONSTRAINT [DF_FileWatcherConfig_UserCreated]  DEFAULT (suser_sname()) FOR [UserCreated]
GO
--------------END CMD FileWatcherConfig table 29-05-2025 Joro, Eli--------------------------


--------------START CMD Database operations tables 05.08.2025 Bobi--------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[DatabaseAdminUser](
	[IDDatabaseAdminUser] [int] IDENTITY(1,1) NOT NULL,
	[LgnName] [nvarchar](50) NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
 CONSTRAINT [IDDatabaseAdminUser] PRIMARY KEY CLUSTERED 
(
	[IDDatabaseAdminUser] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, FILLFACTOR = 95, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[DatabaseAdminUser] ADD  CONSTRAINT [DF_DatabaseAdminUser_TimestampCreated]  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[DatabaseAdminUser] ADD  CONSTRAINT [DF_DatabaseAdminUser_UserCreated]  DEFAULT (suser_sname()) FOR [UserCreated]
GO

CREATE TABLE [dbo].[TemplateScript]
(
    [IDTemplate] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [TemplateName] NVARCHAR(MAX) NULL,
    [Script] NVARCHAR(MAX) NULL,
    [IDService] INT NOT NULL,
    [TimestampCreated] [datetime] NOT NULL,
    [UserCreated] NVARCHAR(128) NOT NULL
);

ALTER TABLE [dbo].[TemplateScript] ADD  CONSTRAINT [DF_TemplateScript_TimestampCreated]  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[TemplateScript] ADD  CONSTRAINT [DF_TemplateScript_UserCreated]  DEFAULT (suser_sname()) FOR [UserCreated]
GO

alter table TemplateScript  add   CONSTRAINT [FK_TemplateScript_Service] FOREIGN KEY ([IDService])
        REFERENCES [dbo].[Service]([IDService])
GO

--------------END CMD Database operations tables 05.08.2025 Bobi--------------------------


CREATE TABLE [dbo].[FileEventType](
	[IDFileEventType] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](128) NULL,
	[AppName] [nvarchar](1024) NULL,
 CONSTRAINT [PK_FileEventType] PRIMARY KEY CLUSTERED 
(
	[IDFileEventType] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 95) ON [PRIMARY]
) ON [PRIMARY]
GO



---- TEST CMD executed 06.08.2025 Stoyan ---------------
