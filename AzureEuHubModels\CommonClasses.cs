﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO.Compression;
using System.Linq;
using System.Net.Http.Json;
using System.Net.Security;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    //public static class SGADGroups
    //{
    //    public const string Admin = @"SATT\Group SGCloud Admin,87be55f7-cef8-4d74-8942-3ac0dff8890b,SG\Department RND";
    //    public const string SuperUser = @"SATT\Group SGCloud SuperUser,dfd322e9-9730-4e38-a210-8447740c7c40,SG\Department RND";
    //    public const string User = @"SATT\Group SGCloud User,70d39629-2c73-4cc0-80e5-52525bb70195,SG\Department RND";
    //    public const string UserPmd = @"SATT\Group SGCloud User PMD,0756a7a3-cff4-4d8c-a9dc-a516e28f2e93,SG\Department RND";
    //    public const string UserPpd = @"SATT\Group SGCloud User PPD,8f73dcc6-7a3a-432e-960d-0289670dafcc,SG\Department RND";
    //    public const string WebApi = @"SATT\Group SGCloud WebAPI,e3f7c22b-61db-4e81-87ef-f39a0e762c00,SG\Department RND";


    //    public const string BatchRecall = @"SATT\Group SGCloud Batch Recall,d34ecc53-4423-46dd-b634-4328bc28a1c1,SG\Department RND";
    //    public const string ProductWithdraw = @"SATT\Group SGCloud Product Withdraw,8a87aa40-d425-4f2b-9c91-67be0e6d2bac,SG\Department RND";

    //}

    public class DropDownValue
    {
        public DropDownValue()
        {

        }
        public DropDownValue(string value, string text)
        {
            Value = value;
            Text = text;
        }
        public DropDownValue(int intValue, string text)
        {
            IntValue = intValue;
            Text = text;
        }
        public int IntValue { get; set; }
        public string Value { get; set; }
        public string Text { get; set; }
    }

    public class AuditTrailProperty
    {
        public AuditTrailProperty()
        {
                    
        }

        public AuditTrailProperty(string property, string newValue, string oldValue = null)
        {
            Property = property;
            NewValue = newValue;
            OldValue = oldValue;
        }

        public string Property { get; set; }
        public string OldValue { get; set; }
        public string NewValue { get; set; }
    }

    public class DbList
    {
        [Key]
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public static class CustomerType
    {
        public const int MAH = 1;
        public const int CMO = 2;
        public const int PARALLEL_DISTIBUTOR = 3;
            
    }


    public static class AppDBSettings
    {
        public const string FOLDER_FILE_IMPORT = "FOLDER_FILE_IMPORT";
        public const string STORAGE_SYSTEM = "STORAGE_SYSTEM";
        public const string AZRUE_STORAGE_CONNECTION_STRING = "AZRUE_STORAGE_CONNECTION_STRING";

        public const string COMPANY_PREFIX_LIST = "COMPANY_PREFIX_LIST";
    }

    public class DbString
    {
        [Key]
        public string Value { get; set; }
    }

    public static class AuditTrailConstants
    {
        public enum ActionEnum
        {
            CREATE,
            UPDATE,
            DELETE,
            APPEND,
            REMOVE,
            ACCEPT,
            REJECT
        }

        public enum TableEnum
        {
            BATCH,
            REPORT,
            APP_LOG,
            PRODUCT,
            MAH,
            TARGET_MARKET,
            SETTING,
            COUNTRY,
            CERTIFICATE
        }

        public enum ScopeEnum
        {
            Batch,
            Report,
            App_Log,
            Event,
            TargetMarket,
            Wholesaler,
            Setting,
            Country,
            Mah,
            Certificate,
            Support,
            AlternateProduct,
            ProductExtend
        }
    }

    public static class Utils
    {
        /// <summary>
        /// GTIN or PPN code utility.
        /// </summary>
        public static class ProductNumber
        {
            /// <summary>
            /// GTIN or PPN code utility.
            /// parameter type = GTIN or PPN
            /// code = code
            /// </summary>
            public static bool Check(string type, string code)
            {
                try
                {
                    if (string.IsNullOrEmpty(type) || string.IsNullOrEmpty(code))
                        return false;

                    if (type == "PPN")
                        return PPN.Check(code);
                    else if (type == "GTIN")
                        return GTIN.Check(code);
                    else
                        return false;

                }
                catch (Exception)
                {
                    return false;
                }
            }


            /// <summary>
            /// GTIN or PPN code utility.
            /// Parameter code = code to be checked. 
            /// Result GTIN or PPN string
            /// </summary>
            public static string GetCodeType(string code)
            {
                if (GTIN.Check(code))
                    return "GTIN";
                else if (PPN.Check(code))
                    return "PPN";
                else
                    throw new Exception("Error! Unknown code type or invalid check sum.");

            }
        }



        /// <summary>
        /// PPN code utility.
        /// </summary>
        public static class PPN
        {
            /// <summary>
            /// PPN code utility.
            /// parameter code = string(12), code to be checked
            /// result true or false depend of checksum recalculating
            /// </summary>
            public static bool Check(string code)
            {
                try
                {
                    if (code.Trim().Length == 12)
                    {
                        string checksum = code.Substring(10, 2);
                        byte[] bytes = Encoding.ASCII.GetBytes(code);
                        int sum = (int)bytes[0] * 2
                            + (int)bytes[1] * 3
                            + (int)bytes[2] * 4
                            + (int)bytes[3] * 5
                            + (int)bytes[4] * 6
                            + (int)bytes[5] * 7
                            + (int)bytes[6] * 8
                            + (int)bytes[7] * 9
                            + (int)bytes[8] * 10
                            + (int)bytes[9] * 11;

                        int intCheckSum = sum % 97;
                        string strCheckSum = intCheckSum.ToString("00");
                        return checksum == strCheckSum;
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// GTIN code utility.
        /// </summary>
        public static class GTIN
        {

            /// <summary>
            /// return GTIN14 code.
            /// </summary>
            /// <param name="gtin">GTIN code to be converted</param>
            /// <returns>
            /// GTIN14 code
            /// </returns>
            public static string ToGTIN14(string code)
            {
                return ("00000000000000" + code).Substring(code.Length);

            }

            /// <summary>
            /// Check for correct checksum in GTIN code.
            /// </summary>
            /// <param name="gtin">GTIN code to be verrified</param>
            /// <returns>
            /// True or false if code is or not correct.
            /// </returns>
            public static bool Check(string gtin)
            {
                try
                {
                    List<int> GTINSizes = new List<int> { 8, 12, 13, 14 };
                    if (GTINSizes.Contains(gtin.Length))
                    {
                        int check;
                        int sum = 0;
                        int mult = 3;
                        for (int i = gtin.Length - 2; i >= 0; i--)
                        {
                            int x = Int32.Parse(gtin[i].ToString());
                            sum += x * mult;
                            if (mult == 3) mult = 1;
                            else mult = 3;
                        }
                        if (sum < 10)
                        {
                            if (sum == 0)
                                check = 0;
                            else
                                check = 10 - sum;
                        }
                        else
                        {
                            check = 10 - (sum % 10);
                            if (check == 10)
                                check = 0;
                        }
                        if (check.ToString() == gtin[gtin.Length - 1].ToString())
                            return true;
                        else
                            return false;
                    }
                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }

            /// <summary>
            /// Calculate correct checksum GTIN code.
            /// </summary>
            /// <param name="gtin">GTIN code without checksum. Accepeted sizes - 7, 11, 12 or 13 simbols </param>
            /// <param name="gtinWithCheckDigit" >Output parameter - GTIN code with checksum</param>
            /// <returns>
            /// True or false if calculation is or not correct.
            /// </returns>
            public static bool GetCheckDigit(string gtin, out string gtinWithCheckDigit)
            {
                try
                {
                    List<int> GTINSizes = new List<int> { 7, 11, 12, 13 };
                    if (GTINSizes.Contains(gtin.Length))
                    {
                        int check;
                        int sum = 0;
                        int mult = 3;
                        for (int i = gtin.Length - 1; i >= 0; i--)
                        {
                            int x = Int32.Parse(gtin[i].ToString());
                            sum += x * mult;
                            if (mult == 3) mult = 1;
                            else mult = 3;
                        }
                        if (sum < 10)
                        {
                            if (sum == 0)
                                check = 0;
                            else
                                check = 10 - sum;
                        }
                        else
                        {
                            check = 10 - (sum % 10);
                            if (check == 10)
                                check = 0;
                        }
                        gtinWithCheckDigit = gtin + check.ToString();
                        return true;
                    }
                    gtinWithCheckDigit = "";
                    return false;
                }
                catch (Exception)
                {
                    gtinWithCheckDigit = "";
                    return false;
                }
            }

        }

        public static class Serial
        {
            public static bool Check(string serial)
            {
                try
                {
                    if (string.IsNullOrEmpty(serial.Trim()))
                        return false;

                    if (string.IsNullOrWhiteSpace(serial.Trim()))
                        return false;

                    string pattern = @"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{1,20})$";
                    Regex rgx = new Regex(pattern);
                    return rgx.IsMatch(serial.Trim());
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }
        public static class StringCompressor
        {
            public static string Decompress(string compressedText)
            {
                byte[] gZipBuffer = Convert.FromBase64String(compressedText);
                using (var memoryStream = new System.IO.MemoryStream())
                {
                    int dataLength = BitConverter.ToInt32(gZipBuffer, 0);
                    memoryStream.Write(gZipBuffer, 4, gZipBuffer.Length - 4);

                    var buffer = new byte[dataLength];

                    memoryStream.Position = 0;
                    using (var gZipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
                    {
                        gZipStream.Read(buffer, 0, buffer.Length);

                    }
                    return Encoding.UTF8.GetString(buffer);
                }
            }
        }
        public static class Batch
        {
            public static bool Check(string batchid)
            {
                try
                {
                    if (string.IsNullOrEmpty(batchid.Trim()))
                        return false;

                    if (string.IsNullOrWhiteSpace(batchid.Trim()))
                        return false;

                    string pattern = @"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{1,20})$";
                    Regex rgx = new Regex(pattern);
                    return rgx.IsMatch(batchid.Trim());
                }
                catch (Exception)
                {
                    return false;
                }
            }


            public static string GetCheckFailedMessage()
            {
                return "Batch ID is expected to be alphanumeric values of up to 20 characters, limited to those characters defined in the GS1 General  Specifications, Version 13.1 (GS1 AI Encodable Character Set 82). These are… !\"%&'()*,-./:;<=>?_ 0123456789 ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            }

        }
    }

    public class AuditTrailItemExport
    {
        public AuditTrailItemExport()
        {

        }

        public AuditTrailItemExport(DateTime timestamp,
                                    int idAuditTrail,
                                    string action,
                                    string displayName,
                                    string oldValue,
                                    string newValue,
                                    string user)
        {
            Timestamp = timestamp;
            IDAuditTrail = idAuditTrail;
            Action = action;
            DisplayName = displayName;
            OldValue = oldValue;
            NewValue = newValue;
            User = user;
        }

        public int IDAuditTrail { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime Timestamp { get; set; }

        public string FieldName { get; set; }

        public string Action { get; set; }

        public string OldValue { get; set; }

        public string NewValue { get; set; }

        public string DisplayName { get; set; }

        public string User { get; set; }
    }

    public class ApiManufacturerResponse
    {
        public bool IsSuccess { get; set; }
        public string ResponseMessage { get; set; }
    }

    public class ProductViewModel
    {
        public int IDProduct { get; set; }
        public string CodeType { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsSuccess { get; set; }
        public string Message { get; set; }

    }
}
