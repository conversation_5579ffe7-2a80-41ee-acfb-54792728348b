{"AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "softgroup.eu", "TenantId": "6db202c3-153d-4367-864a-8b7a54d46b00", "ClientId": "d004fb86-ed51-4a7f-a189-e8ebe245dc95", "ClientSecret": "*************************************", "CallbackPath": "/signin-oidc"}, "AzureAd_Test": {"Instance": "https://login.microsoftonline.com/", "Domain": "satt.systems", "TenantId": "febb5383-d8e4-4bb9-8393-d08f0e71a005", "ClientId": "ae3a78c7-7ac2-4ed6-86d2-c76c3b272e1d", "ClientSecret": "****************************************", "CallbackPath": "/signin-oidc"}, "AzureAd_Prod": {"Instance": "https://login.microsoftonline.com/", "Domain": "satt.systems", "TenantId": "febb5383-d8e4-4bb9-8393-d08f0e71a005", "ClientId": "409ec505-a5a2-4c11-93dc-e3f694a68e04", "ClientSecret": "****************************************", "CallbackPath": "/signin-oidc"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"AzureCmd": "Server=tcp:sgsqlsrv.database.windows.net,1433;Initial Catalog=CloudMasterData;Persist Security Info=False;User ID=SgAzureUserCMD;Password=****$$3umvir@T;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "AzureCmd_Test": "Server=tcp:satt-test.database.windows.net,1433;Initial Catalog=CloudMasterData;Persist Security Info=False;User ID=satt-test-cmdrepl;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "AzureCmd_Prod": "Server=tcp:satt-prod.database.windows.net,1433;Initial Catalog=CloudMasterData;Persist Security Info=False;User ID=satt-prod-cmdrepl;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "UsageStatistics": "Server=tcp:sgsqlsrv.database.windows.net,1433;Initial Catalog=USAGE_STATISTICS_001_WORK;Persist Security Info=True;User ID=us-work-softgroup;Password=*************$rD;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "UsageStatistics_Test": "Server=tcp:satt-test.database.windows.net,1433;Initial Catalog=UsageStatistics-TEST-001;Persist Security Info=True;User ID=satt-test-cmdrepl;Password=****************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "UsageStatistics_Prod": "Server=tcp:satt-prod.database.windows.net,1433;Initial Catalog=UsageStatistics-PROD-001;Persist Security Info=True;User ID=satt-prod-cmdrepl;Password=****************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}, "AllowedHosts": "*", "Azure": {"SignalR": {"Enabled": "true"}}}