﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("SecuritySessionToken")]
    public partial class SecuritySessionToken
    {
        [Key]
        public int IDSecuritySessionToken { get; set; }

        [Required]
        [StringLength(4000)]
        public string NewToken { get; set; }

        public int DurationHour { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        public DateTime? TimestampUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        public Guid? ReferenceID { get; set; }

    }
}
