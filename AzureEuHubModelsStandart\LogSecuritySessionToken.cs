﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("LogSecuritySessionToken")]
    public class LogSecuritySessionToken
    {
        [Key]
        public int IDSessionToken { get; set; }

        [Required]
        [StringLength(4000)]
        public string NewToken { get; set; }

        public int DurationHour { get; set; }

        [StringLength(4000)]
        public string ReferenceID { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

    }
}
