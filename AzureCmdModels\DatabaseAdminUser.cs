﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("DatabaseAdminUser", Schema = "dbo")]
    public class DatabaseAdminUser
    {
        [Key]
        [Column("IDDatabaseAdminUser")]
        public int IDDatabaseAdminUser { get; set; }

        [Required]
        [MaxLength(50)]
        public string LgnName { get; set; }

        [Required]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [MaxLength(128)]
        public string UserCreated { get; set; }
    }
}
