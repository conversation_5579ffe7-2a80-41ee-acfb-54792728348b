﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("PackVerificationError")]
    public class PackVerificationError
    {
        [Key]
        public long IDPackVerificationError { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [StringLength(20)]
        public string SerialNumber { get; set; }

        [StringLength(10)]
        public string Code { get; set; }
    }
}
