﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("ProductTargetMarket")]
    public class ProductTargetMarket
    {
        public ProductTargetMarket()
        {
            Wholesalers = new HashSet<Wholesaler>();
        }

        [Key]
        public int IDProductTargetMarket { get; set; }

        public int IDProduct { get; set; }

        [Required]
        public string Market { get; set; }

        public bool SerializationFlag { get; set; }

        [StringLength(50)]
        public string NationalCode { get; set; }

        [StringLength(50)]
        public string MahID { get; set; }

        [StringLength(100)]
        public string MahName { get; set; }

        [StringLength(255)]
        public string MahStreet { get; set; }

        [StringLength(255)]
        public string MahStreet2 { get; set; }

        [StringLength(100)]
        public string MahCity { get; set; }

        [StringLength(50)]
        public string MahPostCode { get; set; }

        public string MahCountryCode { get; set; }

        public bool? WhitelistFlag { get; set; }

        [StringLength(60)]
        public string Article57Code { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("ProductTargetMarket")]
        public ICollection<Wholesaler> Wholesalers { get; set; }

        [InverseProperty("ProductTargetMarkets")]
        public TargetMarket TargetMarket { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((ProductTargetMarket)obj).IDProductTargetMarket == this.IDProductTargetMarket)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }

    }
}
