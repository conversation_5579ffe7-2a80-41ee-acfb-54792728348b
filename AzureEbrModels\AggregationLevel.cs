﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("AggregationLevel")]
    public class AggregationLevel
    {
        public AggregationLevel()
        {
            Machines = new HashSet<Machine>();
        }

        [Key]
        public int IDAggregationLevel { get; set; }

        [Required]
        public int SortOrder { get; set; }

        [Required]
        [StringLength(100)]
        public string Unit { get; set; }

        [Required]
        [StringLength(20)]
        public string SerialType { get; set; }

        [Required]
        [StringLength(100)]
        public string Item { get; set; }

        [Required]
        [StringLength(20)]
        public string ItemSerialType { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("AggregationLevel")]
        public ICollection<Machine> Machines { get; set; }
    }
}
