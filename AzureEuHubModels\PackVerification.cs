﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("PackVerification")]
    public class PackVerification
    {
        [Key]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? Timestamp { get; set; }

        [StringLength(10)]
        public string ProductCodeType { get; set; }

        [StringLength(50)]
        public string ProductCode { get; set; }

        [StringLength(10)]
        public string ErrorCode { get; set; }

        public string ErrorDescription { get; set; }
    }
}
