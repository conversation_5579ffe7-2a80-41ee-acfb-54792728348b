﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("EventType")]
    public class EventType
    {
        [Key]
        public int IDEventType { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; }

        public bool HasSerial { get; set; }

        public bool HasMarket { get; set; }

        public bool IsSerialsCreate { get; set; }

        public bool IsSerialsStateChange { get; set; }

        public bool IsBatchUpdate { get; set; }

        public bool IsBatchRecall { get; set; }

        public bool IsRepackBufferCreate { get; set; }

        public bool IsPackVerification { get; set; }

        public int Icon { get; set; }
    }
}
