﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureEuHubModelsStandart
{
    [Table("BatchTargetMarket")]
    public class BatchTargetMarket
    {

        [Key]
        public int IDBatchTargetMarket { get; set; }

        public int IDBatch { get; set; }

        public int IDProductTargetMarket { get; set; }

        [Required]
        public string Market { get; set; }

        public bool SerializationFlag { get; set; }

        [StringLength(50)]
        public string NationalCode { get; set; }

        [StringLength(50)]
        public string MahId { get; set; }

        [StringLength(100)]
        public string MahName { get; set; }

        [StringLength(255)]
        public string MahStreet { get; set; }

        [StringLength(255)]
        public string MahStreet2 { get; set; }

        [StringLength(100)]
        public string MahCity { get; set; }

        [StringLength(50)]
        public string MahPostCode { get; set; }

        public string MahCountryCode { get; set; }

        public bool? WhitelistFlag { get; set; }

        [StringLength(60)]
        public string Article57Code { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

    }
}
