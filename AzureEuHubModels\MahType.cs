﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("MahType")]
    public class MahType
    {
        public MahType()
        {
            Mahs = new HashSet<Mah>();
        }

        [Key]
        public int IDMahType { get; set; }

        [StringLength(50)]
        public string Type { get; set; }

        [InverseProperty("MahType")]
        public ICollection<Mah> Mahs { get; set; }
    }
}
