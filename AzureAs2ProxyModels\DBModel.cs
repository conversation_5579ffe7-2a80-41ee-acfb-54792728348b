﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Reflection.Emit;

namespace AzureAs2ProxyModels
{
    public class DBModel : DbContext
    {
        public virtual DbSet<Request> Requests { get; set; }
        public virtual DbSet<AppSetting> AppSettings { get; set; }


        public DBModel(DbContextOptions<DBModel> options) : base(options)
        {
            Database.SetCommandTimeout(600);
        }

        public DBModel()
        {
            Database.SetCommandTimeout(600);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

        }
    }
}
