<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project>
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <ResourceId>/subscriptions/dea32593-835d-4cf7-ba86-5c7dff44982e/resourceGroups/PROD_RESOURCE_GROUP/providers/Microsoft.Web/sites/SATT-StatisticsAPI-PROD</ResourceId>
    <ResourceGroup>PROD_RESOURCE_GROUP</ResourceGroup>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://satt-statisticsapi-prod.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <ExcludeApp_Data>false</ExcludeApp_Data>
    <ProjectGuid>460c26f1-fa50-4dca-8c3a-6093445aeb21</ProjectGuid>
    <MSDeployServiceURL>satt-statisticsapi-prod.scm.azurewebsites.net:443</MSDeployServiceURL>
    <DeployIisAppPath>SATT-StatisticsAPI-PROD</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>true</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>true</EnableMSDeployBackup>
    <EnableMsDeployAppOffline>true</EnableMsDeployAppOffline>
    <UserName>$SATT-StatisticsAPI-PROD</UserName>
    <_SavePWD>true</_SavePWD>
    <_DestinationType>AzureWebSite</_DestinationType>
    <InstallAspNetCoreSiteExtension>false</InstallAspNetCoreSiteExtension>
  </PropertyGroup>
</Project>