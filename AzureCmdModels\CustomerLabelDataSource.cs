﻿using AzureCmdModels;
using SGAuditTrail;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureCmdModels
{
    [Table("CustomerLabelDataSource")]
    [Audit(DisplayName = "Customer label data source")]
    public class CustomerLabelDataSource
    {
        [Key]
        public int IDCustomerDataSource { get; set; }

        [Required]
        public int IDCustomer { get; set; }


        [Column("DataSource")]
        [DisplayName("Data Source")]
        public string DataSource { get; set; }

        [Required]
        [Audit(DisplayName = "Data source name")]
        public string Name { get; set; }

        [InverseProperty("CustomerLabelDataSources")]
        public Customer Customer { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [Required]
        [Audit(DisplayName = "Unit")]

        public string Unit { get; set; }

        public string SerialType { get; set; }

        public string Item { get; set; }

        public string ItemSerialType { get; set; }

        [Column("SrcSystemName")]
        [DisplayName("Source system")]
        [Audit(DisplayName = "Source system")]
        public string SrcSystemName { get; set; }

        [Column("SrcSystem")]
        [DisplayName("Source system")]
        public string SrcSystem { get; set; }

        [NotMapped]
       // [RequireWhenEBRPPD]
        public int? IDBatch { get; set; }

        [NotMapped]
        [Required]
        [DisplayName("Data Source Provider")]

        public DataSourceProviderEnum? DataSourceProvider { get; set; }

        [NotMapped]
        [DisplayName("Serial number")]
        public string SerialNumber { get; set; }


    }
    public class RequireWhenEBRPPDAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var customerLabelDataSource = (CustomerLabelDataSource)validationContext.ObjectInstance;
            if (customerLabelDataSource.DataSourceProvider == DataSourceProviderEnum.FILE)// when source provider is file, IDBatch is not required
                return ValidationResult.Success;
            else
            {
                int? iDBatch = value as int?;
                return !iDBatch.HasValue ? new ValidationResult("IDBatch is required.")  : ValidationResult.Success;
            }

        }
    }
}
