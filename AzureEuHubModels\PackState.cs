﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("PackState")]
    public class PackState
    {
        public PackState()
        {
            PackStatusChangeRuleSourceStatus = new HashSet<PackStatusChangeRule>();
            PackStatusChangeRuleTargetStatus = new HashSet<PackStatusChangeRule>();
            Serials = new HashSet<Serial>();
        }

        [Key]
        public short IDPackState { get; set; }

        [Required]
        [StringLength(50)]
        public string HubName { get; set; }

        [StringLength(50)]
        public string Name { get; set; }

        [InverseProperty("PackStatusSourceStatus")]
        public ICollection<PackStatusChangeRule> PackStatusChangeRuleSourceStatus { get; set; }

        [InverseProperty("PackStatusTargetStatus")]
        public ICollection<PackStatusChangeRule> PackStatusChangeRuleTargetStatus { get; set; }

        [InverseProperty("PackState")]
        public ICollection<Serial> Serials { get; set; }
    }
}
