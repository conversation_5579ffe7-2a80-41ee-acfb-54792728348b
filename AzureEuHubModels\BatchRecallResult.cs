﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("BatchRecallResult")]
    public class BatchRecallResult
    {
        [Key]
        public long IDBatchRecallResult { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime Timestamp { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        [StringLength(50)]
        public string SourceType { get; set; }

        [StringLength(50)]
        public string SourceCode { get; set; }

        [StringLength(20)]
        public string RecallCount { get; set; }

        [StringLength(10)]
        public string ErrorCode { get; set; }

        public string ErrorDescription { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((BatchRecallResult)obj).IDBatchRecallResult == this.IDBatchRecallResult)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }
    }
}
