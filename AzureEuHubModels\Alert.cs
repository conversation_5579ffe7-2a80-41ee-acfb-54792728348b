﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{

    [Table("Alert")]
    public class Alert
    {
        public Alert()
        {
            AlertItems = new HashSet<AlertItem>();
        }

        [Key]
        public int IDAlert { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? AlertDateTime { get; set; }

        [StringLength(50)]
        public string Code { get; set; }

        [StringLength(50)]
        public string ProductCodeType { get; set; }

        [StringLength(14)]
        public string ProductCode { get; set; }

        [StringLength(200)]
        public string UniqueAlertId { get; set; }

        public string Message { get; set; }

        [StringLength(1024)]
        public string Source { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [InverseProperty("Alert")]
        public ICollection<AlertItem> AlertItems { get; set; }
    }
}
