﻿using SgXmlEvent;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;

namespace AzureEbrModels
{
    [Table("Batch")]
    public class Batch
    {
        public Batch()
        {
            VMOrders = new HashSet<VMOrder>();
            BatchAggregations = new HashSet<BatchAggregation>();
            BatchErrors = new HashSet<BatchError>();
            BatchExtends = new HashSet<BatchExtend>();
            Machines = new HashSet<Machine>();
            Reports = new HashSet<Report>();
            BatchTemplateLevelFiles = new HashSet<BatchTemplateLevelFile>();
        }

        [Key]
        public int IDBatch { get; set; }

        [Required]
        public int? IDProduct { get; set; }

        [Required]
        [StringLength(100)]
        [DisplayName("Batch ID")]
        [RegularExpression(@"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{1,20})$",
         ErrorMessage = "Batch ID is expected to be alphanumeric values of up to 20 characters, limited to those characters defined in the GS1 General  Specifications.")]
        public string BatchID { get; set; }

        [Required]
        public int IDState { get; set; }

        [Required]
        public int IDLine { get; set; }

        [Required]
        [DisplayName("Expiry date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? ExpiryDate { get; set; }

        public bool ExpiryDateZeroDay { get; set; }

        [DisplayName("Manufacturing date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? ManufacturingDate { get; set; }

        [Required(ErrorMessage = "The Target market field is required.")]
        [DisplayName("Target market")]
        public int? IDTargetMarket { get; set; }

        public bool HasSerialization { get; set; }
        public bool HasAggregation { get; set; }

        [StringLength(100)]
        public string ProductionOrder { get; set; }

        public DateTime? ProductionOrderDate { get; set; }

        public int? IDTemplate { get; set; }

        public int? ProductionQuantity { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("Batches")]
        public Line Line { get; set; }

        [InverseProperty("Batches")]
        public TargetMarket TargetMarket { get; set; }

        [InverseProperty("Batches")]
        public BatchState BatchState { get; set; }

        [InverseProperty("Batch")]
        public ICollection<BatchAggregation> BatchAggregations { get; set; }

        [InverseProperty("Batch")]
        public ICollection<BatchError> BatchErrors { get; set; }

        [InverseProperty("Batch")]
        public ICollection<BatchExtend> BatchExtends { get; set; }

        [InverseProperty("Batch")]
        public ICollection<Machine> Machines { get; set; }

        [InverseProperty("Batch")]
        public ICollection<VMOrder> VMOrders { get; set; }

        [InverseProperty("Batch")]
        public BatchProduct BatchProduct { get; set; }

        [InverseProperty("Batch")]
        public BatchSerialization BatchSerialization { get; set; }

        [InverseProperty("Batch")]
        public ICollection<Report> Reports { get; set; }

        [InverseProperty("Batch")]
        public ICollection<BatchTemplateLevelFile> BatchTemplateLevelFiles { get; set; }

        public ObservableCollection<SgXmlEventValidationProcess> ValidationProcess { get; private set; } = new ObservableCollection<SgXmlEventValidationProcess>();

        public Batch Clone()
        {
            Batch newBatch = new Batch
            {
                IDBatch = this.IDBatch,
                BatchID = this.BatchID,
                ExpiryDate = this.ExpiryDate,
                ExpiryDateZeroDay = this.ExpiryDateZeroDay,
                ManufacturingDate = this.ManufacturingDate,
                IDTargetMarket = this.IDTargetMarket,
                UserCreated = this.UserCreated,
                TimestampCreated = this.TimestampCreated,
                IDProduct = this.IDProduct,
                IDLine = this.IDLine,
                IDState = this.IDState,
                ProductionOrder = this.ProductionOrder,
                ProductionOrderDate = this.ProductionOrderDate,
                ProductionQuantity = this.ProductionQuantity,
                BatchExtends = this.BatchExtends
                    .Select(be => new BatchExtend
                    {
                        IDBatchExtend = be.IDBatchExtend,
                        Name = be.Name,
                        GS1AICode = be.GS1AICode,
                        StringValue = be.StringValue,
                        Regex = be.Regex,
                        IsRequired = be.IsRequired,
                        IsSelectValue = be.IsSelectValue,
                        Type = be.Type,
                        IDExtendProperty = be.IDExtendProperty,
                        TimestampCreated = be.TimestampCreated,
                        UserCreated = be.UserCreated,
                        TimestampUpdated = be.TimestampUpdated,
                        UserUpdated = be.UserUpdated
                    }).ToList()
            };

            return newBatch;
        }

        public List<BatchAggregation> CloneAggregation()
        {

            var result = this.BatchAggregations
                .Select(ba => new BatchAggregation()
                {
                    IDBatchAggregation = ba.IDBatchAggregation,
                    DefPoolCode = ba.DefPoolCode,
                    DefPoolRequestElevation = ba.DefPoolRequestElevation,
                    IDBatch = ba.IDBatch,
                    Item = ba.Item,
                    ItemQuantity = ba.ItemQuantity,
                    SerialType = ba.SerialType,
                    ItemSerialType = ba.ItemSerialType,
                    SSCCCopmanyPrefix = ba.SSCCCopmanyPrefix,
                    SSCCExtensionDigit = ba.SSCCExtensionDigit,
                    Unit = ba.Unit,
                    UnitQuantity = ba.UnitQuantity,
                    TimestampCreated = ba.TimestampCreated,
                    UserCreated = ba.UserCreated,
                    TimestampUpdated = ba.TimestampUpdated,
                    UserUpdated = ba.UserUpdated,
                    GTIN = ba.GTIN,
                    BatchAggregationExtends = ba.BatchAggregationExtends
                        .Select(a => new BatchAggregationExtend()
                        {
                            IDBatchAggregationExtend = a.IDBatchAggregationExtend,
                            Name = a.Name,
                            GS1AICode = a.GS1AICode,
                            StringValue = a.StringValue,
                            Regex = a.Regex,
                            IsRequired = a.IsRequired,
                            IsSelectValue = a.IsSelectValue,
                            Type = a.Type,
                            IDExtendProperty = a.IDExtendProperty,
                            TimestampCreated = a.TimestampCreated,
                            UserCreated = a.UserCreated,
                        }).ToList()
                })
                .ToList();

            return result;
        }

        public string GetDataSourceByLevel(string level, string serialNumber)
        {
            //Load data for level

            var batchAggregation = this.BatchAggregations
                    .FirstOrDefault(b => b.Unit == level);
            if (batchAggregation == null)
                throw new Exception("Aggregation level not found");

            BatchAggregationSerial serial;
            if (batchAggregation.BatchAggregationSerials != null && batchAggregation.BatchAggregationSerials.Count > 0)
            {
                if (string.IsNullOrEmpty(serialNumber))
                {
                    serial = batchAggregation.BatchAggregationSerials.FirstOrDefault();
                }
                else
                {
                    serial = batchAggregation.BatchAggregationSerials
                    .FirstOrDefault(bas => bas.SerialNumber == serialNumber);
                    if (serial == null)
                        throw new Exception("Serial number not found");
                }
            }
            else
                throw new Exception("No serials found in batch aggregation");


            //Form xml file
            //var product = Produc
            XmlDocument document = new XmlDocument();
            var batchXML = new BatchPlainXMLcs
            {
                Label = new ThermalLabelDataSource
                {
                    BatchID = this.BatchID,
                    ProductCode = this.BatchProduct.Code,
                    ProductName = this.BatchProduct.Name,
                    ExpiryDate = this.ExpiryDate,
                    ManufacturingDate = this.ManufacturingDate,
                    TargetMarketShortName = this.TargetMarket?.ShortName,
                    TargetMarketName = this.TargetMarket?.Name,
                    Type = batchAggregation.SerialType,
                    AggregationLevel = batchAggregation.Unit,
                    UnitCapacity = batchAggregation.UnitQuantity,
                    ItemLevel = batchAggregation.Item,
                    ItemCount = batchAggregation.ItemQuantity.Value,
                    ItemType = batchAggregation.ItemSerialType,
                    SerialNumber = serial.SerialNumber,
                    InternalCode = this.BatchProduct.InternalCode,
                    InternalCodeType = this.BatchProduct.InternalCodeType,
                    Form = this.BatchProduct.Form,
                    Strength = this.BatchProduct.Strength,
                    PackSize = this.BatchProduct.PackSize.Value,
                    PackType = this.BatchProduct.PackType
                }
            };
            document.LoadXml(batchXML.XmlToString());


            XmlNode xmlNode = document.SelectSingleNode("//Label");
            foreach (var item in this.BatchExtends)
            {
                AddXMLElement(document, xmlNode, item);

            }
            foreach (var item in this.BatchProduct.BatchProductAdditionalCodes)
            {
                AddXMLElement(document, xmlNode, item);

            }
            foreach (var item in batchAggregation.BatchAggregationExtends)
            {
                AddXMLElement(document, xmlNode, item, level);
            }
            //return stringFile
            return document.OuterXml;
        }

        private static void AddXMLElement(XmlDocument document, XmlNode xmlNode, BatchExtend item)
        {
            XmlElement extendedElement;
            if (item.IsSelectValue)
            {

                extendedElement = document.CreateElement(Regex.Replace(item.Name, @"\s+", "_") + ".Key");
                extendedElement.InnerText = item.StringValue;
                xmlNode.AppendChild(extendedElement);
                extendedElement = document.CreateElement(Regex.Replace(item.Name, @"\s+", "_") + ".Value");
                extendedElement.InnerText = item.DisplayText;
                xmlNode.AppendChild(extendedElement);
                if (item.GS1AICode != null)
                {
                    extendedElement = document.CreateElement(Regex.Replace(item.Name, @"\s+", "_") + "AICode");
                    extendedElement.InnerText = item.GS1AICode;
                    xmlNode.AppendChild(extendedElement);
                }

            }
            else
            {
                extendedElement = document.CreateElement(Regex.Replace(item.Name, @"\s+", "_"));
                extendedElement.InnerText = item.StringValue;
                xmlNode.AppendChild(extendedElement);
                if (item.GS1AICode != null)
                {
                    extendedElement = document.CreateElement(Regex.Replace(item.Name, @"\s+", "_") + "AICode");
                    extendedElement.InnerText = item.GS1AICode;
                    xmlNode.AppendChild(extendedElement);
                }
            }

        }
        private static void AddXMLElement(XmlDocument document, XmlNode xmlNode, BatchAggregationExtend extendProperty, string level)
        {
            XmlElement extendedElement;
            if (extendProperty.IsSelectValue)
            {
                extendedElement = document.CreateElement(Regex.Replace("Aggregation." + level + "." + extendProperty.Name, @"\s+", "_") + ".Key");
                extendedElement.InnerText = extendProperty.StringValue;
                xmlNode.AppendChild(extendedElement);
                extendedElement = document.CreateElement(Regex.Replace("Aggregation." + level + "." + extendProperty.Name, @"\s+", "_") + ".Value");
                extendedElement.InnerText = extendProperty.DisplayText;
                xmlNode.AppendChild(extendedElement);

                if (extendProperty.GS1AICode != null)
                {
                    extendedElement = document.CreateElement(Regex.Replace("Aggregation." + level + "." + extendProperty.Name, @"\s+", "_") + "AICode");
                    extendedElement.InnerText = extendProperty.GS1AICode;
                    xmlNode.AppendChild(extendedElement);
                }
            }
            else
            {
                extendedElement = document.CreateElement(Regex.Replace(extendProperty.Name, @"\s+", "_"));
                extendedElement.InnerText = extendProperty.StringValue;
                xmlNode.AppendChild(extendedElement);
                if (extendProperty.GS1AICode != null)
                {
                    extendedElement = document.CreateElement(Regex.Replace(extendProperty.Name, @"\s+", "_") + "AICode");
                    extendedElement.InnerText = extendProperty.GS1AICode;
                    xmlNode.AppendChild(extendedElement);
                }
            }

        }
        private static void AddXMLElement(XmlDocument document, XmlNode xmlNode, BatchProductAdditionalCode item)
        {
            XmlElement extendedElement;
            {
                extendedElement = document.CreateElement(Regex.Replace("Product_" + item.CodeType, @"\s+", "_"));
                extendedElement.InnerText = item.Code;
                xmlNode.AppendChild(extendedElement);
            }

        }
        private SgVerificationResult sgVerificationResult;

        public bool ValidateData(string noSerializationType,
                                 List<BatchSerializationSerial> serials,
                                 List<BatchAggregation> aggregationLevels,
                                 List<string> validationSteps,
                                 string lgnName,
                                 List<EbrLevels> ebrLevels)
        {
            DateTime startTimer = DateTime.Now;

            List<BatchError> result = new List<BatchError>();

            sgVerificationResult = new SgVerificationResult();

            if (validationSteps.Contains("CheckCorrectExpiryDate"))
            {
                startTimer = DateTime.Now;
                SgXmlEventValidationProcess proc0 = new SgXmlEventValidationProcess("Check batch data");
                AddValdationProcess(proc0);
                var res0 = CheckLineSettings(noSerializationType);
                CheckValidationProcess(proc0, res0, startTimer);
                UpdateValdationProcess(proc0);
                result.AddRange(res0);
            }


            if (validationSteps.Contains("CheckLineSettings"))
            {
                startTimer = DateTime.Now;
                SgXmlEventValidationProcess proc1 = new SgXmlEventValidationProcess("Check line settings");
                AddValdationProcess(proc1);
                var res1 = CheckLineSettings(noSerializationType);
                CheckValidationProcess(proc1, res1, startTimer);
                UpdateValdationProcess(proc1);
                result.AddRange(res1);
            }

            if (validationSteps.Contains("ValidateAssignedSerials"))
            {
                startTimer = DateTime.Now;
                SgXmlEventValidationProcess proc2 = new SgXmlEventValidationProcess("Validate assigned serials");
                AddValdationProcess(proc2);
                var res2 = ValidateAssignedSerials(serials.Count, aggregationLevels);
                CheckValidationProcess(proc2, res2.ToList(), startTimer);
                UpdateValdationProcess(proc2);
                result.AddRange(res2);
            }

            if (validationSteps.Contains("CheckBatchData"))
            {
                startTimer = DateTime.Now;
                SgXmlEventValidationProcess proc3 = new SgXmlEventValidationProcess("Validate batch data");
                AddValdationProcess(proc3);
                var res3 = CheckBatchData();
                CheckValidationProcess(proc3, res3.ToList(), startTimer);
                UpdateValdationProcess(proc3);
                result.AddRange(res3);
            }

            if (validationSteps.Contains("ValidateBatchExtend"))
            {
                startTimer = DateTime.Now;
                SgXmlEventValidationProcess proc4 = new SgXmlEventValidationProcess("Validate batch extend");
                AddValdationProcess(proc4);
                var res4 = ValidateBatchExtend();
                CheckValidationProcess(proc4, res4.ToList(), startTimer);
                UpdateValdationProcess(proc4);
                result.AddRange(res4);
            }

            if (this.HasSerialization)
            {
                if (validationSteps.Contains("ValidateBatchSerialization"))
                {
                    startTimer = DateTime.Now;
                    SgXmlEventValidationProcess proc5 = new SgXmlEventValidationProcess("Validate batch serialization");
                    AddValdationProcess(proc5);
                    var res5 = ValidateBatchSerialization();
                    CheckValidationProcess(proc5, res5.ToList(), startTimer);
                    UpdateValdationProcess(proc5);
                    result.AddRange(res5);
                }


                if (this.BatchSerialization.BatchSerializationExtends != null && this.BatchSerialization.BatchSerializationExtends.Count > 0)
                {
                    if (validationSteps.Contains("ValidateBatchSerializationExtend"))
                    {
                        startTimer = DateTime.Now;
                        SgXmlEventValidationProcess proc6 = new SgXmlEventValidationProcess("Validate batch serialization properties");
                        AddValdationProcess(proc6);
                        var res6 = ValidateBatchSerializationExtend();
                        CheckValidationProcess(proc6, res6.ToList(), startTimer);
                        UpdateValdationProcess(proc6);
                        result.AddRange(res6);
                    }
                }
            }

            if (this.HasAggregation)
            {
                if (validationSteps.Contains("ValidateBatchAggregation"))
                {
                    startTimer = DateTime.Now;
                    SgXmlEventValidationProcess proc7 = new SgXmlEventValidationProcess("Validate batch aggregation");
                    AddValdationProcess(proc7);
                    var res7 = ValidateBatchAggregation();
                    CheckValidationProcess(proc7, res7.ToList(), startTimer);
                    UpdateValdationProcess(proc7);
                    result.AddRange(res7);
                }

                if (this.BatchAggregations.Any(b => b.BatchAggregationExtends != null && b.BatchAggregationExtends.Count > 0)
                        && validationSteps.Contains("ValidateBatchAggregationExtend"))
                {
                    startTimer = DateTime.Now;
                    SgXmlEventValidationProcess proc8 = new SgXmlEventValidationProcess("Validate batch aggregation properties");
                    AddValdationProcess(proc8);
                    var res8 = ValidateBatchAggregationExtend();
                    CheckValidationProcess(proc8, res8.ToList(), startTimer);
                    UpdateValdationProcess(proc8);
                    result.AddRange(res8);
                }
            }



            SgXmlEvent.SgXmlEvent sgXmlEvent = LoadXmlFromBatch(serials, aggregationLevels, ebrLevels);

            sgXmlEvent.ValidateEBR(out SgVerificationResult outSgVerificationResult,
                                   new List<string> { SerialStateEnum.COMMISSIONED.ToString() },
                                   validationSteps);

            if (outSgVerificationResult != null)
                sgVerificationResult = outSgVerificationResult;

            foreach (var vproc in sgXmlEvent.ValidationProcess)
            {
                ValidationProcess.Add(vproc);
            }

            if (sgVerificationResult.IsError || sgVerificationResult.IsWarning)
            {
                result.AddRange(sgVerificationResult.Errors
                    .Select(e => new BatchError
                    {
                        IDBatch = this.IDBatch,
                        IsCrirical = !e.IsSkipAllowed,
                        Message = e.Error,
                        UnitValue = e.SerialNumber,
                        UnitLevel = e.Level,
                        ItemValue = e.ItemSerialNumber,
                        ItemLevel = e.ItemSerialType,
                        TimestampCreated = DateTime.Now,
                        UserCreated = lgnName
                    }));
            }

            if (result != null && result.Count > 0)
            {
                this.BatchErrors = result;
                return true;
            }
            else
            {
                this.BatchErrors = null;
                return false;
            }
        }

        private IEnumerable<BatchError> ValidateAssignedSerials(int batchSerializationSerialCount, List<BatchAggregation> aggregationLevels)
        {
            List<BatchError> result = new List<BatchError>();

            if (this.HasSerialization)
            {
                if (this.BatchSerialization.ProductionQuantity > batchSerializationSerialCount)
                {
                    result.Add(new BatchError
                    {
                        IsCrirical = true,
                        UnitLevel = PackLevel.SERIALIZED_UNIT,
                        UnitValue = $"{batchSerializationSerialCount} of {this.BatchSerialization.ProductionQuantity}",
                        Message = $"NOT ENOUGH SERIALS"
                    });
                }
            }

            if (this.HasAggregation)
            {
                foreach (var level in aggregationLevels)
                {
                    if (level.UnitQuantity > level.BatchAggregationSerials.Count)
                    {
                        result.Add(new BatchError
                        {
                            IsCrirical = true,
                            UnitLevel = level.Unit,
                            UnitValue = $"{level.BatchAggregationSerials.Count} of {level.UnitQuantity}",
                            Message = $"NOT ENOUGH SERIALS"
                        });
                    }
                }
            }


            return result;
        }

        private List<BatchError> CheckBatchData()
        {
            List<BatchError> result = new List<BatchError>();
            if (!Utils.Batch.Check(BatchID))
                result.Add(new BatchError()
                {
                    IsCrirical = true,
                    UnitLevel = "BATCH",
                    UnitValue = "Batch ID",
                    Message = "Not GS1 compatible batch ID"
                });

            if (!ExpiryDate.HasValue || ExpiryDate < DateTime.Now)
                result.Add(new BatchError()
                {
                    IsCrirical = true,
                    UnitLevel = "BATCH",
                    UnitValue = "Expiry Date",
                    Message = "Expiry date missing or is incorrect."
                });

            //TODO validate all BATCH properties  like SAVE button in edit batch page
            //TODO validate serialization and aggragtion properties like edit in batch page

            return result;
        }

        private List<BatchError> ValidateBatchExtend()
        {
            List<BatchError> result = new List<BatchError>();

            if (this.BatchExtends == null)
                return result;

            result.AddRange(this.BatchExtends
                .Where(b => !CheckPropertyValueType(b.Type, b.StringValue))
                .Select(b => new BatchError
                {
                    IsCrirical = true,
                    UnitLevel = "BATCH",
                    UnitValue = null,
                    Message = $"Value {b.StringValue} cannot be converted to type {b.Type} ."
                })
                .ToList());

            result.AddRange(this.BatchExtends
                .Where(be => (be.IsRequired && string.IsNullOrEmpty(be.StringValue)))
                .Select(be => new BatchError()
                {
                    IsCrirical = true,
                    UnitLevel = "BATCH",
                    UnitValue = null,
                    Message = $"{be.Name} is required"
                })
                .ToList());

            result.AddRange(this.BatchExtends.Where(be => !string.IsNullOrEmpty(be.Regex)
                && !string.IsNullOrEmpty(be.StringValue) && !new Regex(be.Regex).IsMatch(be.StringValue))
                .Select(be => new BatchError
                {
                    IsCrirical = true,
                    UnitLevel = "BATCH",
                    UnitValue = null,
                    Message = $"Entered value for {be.Name} does Regex criteria."
                })
                .ToList());

            return result;
        }

        private List<BatchError> ValidateBatchSerialization()
        {
            var result = new List<BatchError>();

            if (this.BatchSerialization == null)
                return result;

            if (string.IsNullOrEmpty(this.BatchSerialization.SerializationType))
            {
                result.Add(new BatchError()
                {
                    IsCrirical = true,
                    UnitLevel = "SERIALIZATION",
                    UnitValue = null,
                    Message = $"Missing serialization type"
                });
            }

            if (this.BatchSerialization.ProductionQuantity == null || this.BatchSerialization.ProductionQuantity < 1)
            {
                result.Add(new BatchError()
                {
                    IsCrirical = true,
                    UnitLevel = "SERIALIZATION",
                    UnitValue = null,
                    Message = $"Missing production quantity"
                });
            }

            return result;
        }

        private List<BatchError> ValidateBatchSerializationExtend()
        {
            var result = new List<BatchError>();

            result.AddRange(this.BatchSerialization.BatchSerializationExtends
                .Where(b => !CheckPropertyValueType(b.Type, b.StringValue))
                .Select(b => new BatchError
                {
                    IsCrirical = true,
                    UnitLevel = "BATCH",
                    UnitValue = null,
                    Message = $"Value {b.StringValue} cannot be converted to type {b.Type} ."
                })
                .ToList());

            result.AddRange(this.BatchSerialization.BatchSerializationExtends
                .Where(be => (be.IsRequired && string.IsNullOrEmpty(be.StringValue)))
                .Select(be => new BatchError()
                {
                    IsCrirical = true,
                    UnitLevel = "SERIALIZATION",
                    UnitValue = null,
                    Message = $"{be.Name} is required"
                })
                .ToList());

            result.AddRange(this.BatchSerialization.BatchSerializationExtends.Where(be => !string.IsNullOrEmpty(be.Regex)
                && !string.IsNullOrEmpty(be.StringValue) && !new Regex(be.Regex).IsMatch(be.StringValue))
            .Select(be => new BatchError
            {
                IsCrirical = true,
                UnitLevel = "SERIALIZATION",
                UnitValue = null,
                Message = $"Entered value for {be.Name} does not match format ({be.Regex})."
            })
            .ToList());

            return result;
        }

        private List<AzureEbrModels.BatchError> ValidateBatchAggregation()
        {

            var result = new List<AzureEbrModels.BatchError>();
            if (this.BatchAggregations == null || this.BatchAggregations.Count == 0)
                return result;

            result.AddRange(this.BatchAggregations
                .Where(ba => !ba.UnitQuantity.HasValue)
                .Select(ba => new AzureEbrModels.BatchError()
                {
                    IsCrirical = true,
                    UnitLevel = "AGGREGATION",
                    UnitValue = null,
                    Message = $"Missing value for field unit quantity in level {ba.Unit} "
                })
                .ToList());

            result.AddRange(this.BatchAggregations
                .Where(ba => !ba.ItemQuantity.HasValue)
                .Select(ba => new AzureEbrModels.BatchError()
                {
                    IsCrirical = true,
                    UnitLevel = "AGGREGATION",
                    UnitValue = null,
                    Message = $"Missing value for field item quantity in level {ba.Unit}"
                })
                .ToList());

            var aggrLevelWithSerializedUnit = this.BatchAggregations.FirstOrDefault(a => a.Item == SgXmlEvent.PackLevel.SERIALIZED_UNIT);
            if (this.BatchSerialization != null
                && this.BatchSerialization.ProductionQuantity.HasValue
                && aggrLevelWithSerializedUnit != null
                && aggrLevelWithSerializedUnit.ItemQuantity.HasValue)
            {
                if (!(aggrLevelWithSerializedUnit.UnitQuantity.Value * aggrLevelWithSerializedUnit.ItemQuantity.Value - this.BatchSerialization.ProductionQuantity < aggrLevelWithSerializedUnit.ItemQuantity.Value))
                {
                    result.Add(new BatchError()
                    {
                        IsCrirical = true,
                        UnitLevel = "AGGREGATION",
                        UnitValue = null,
                        Message = $"Production quantity is not enough for level {aggrLevelWithSerializedUnit.Unit} ({aggrLevelWithSerializedUnit.UnitQuantity.Value}x{aggrLevelWithSerializedUnit.ItemQuantity.Value})."
                    });
                }
            }
            else if (this.BatchSerialization != null
                && this.BatchSerialization.ProductionQuantity.HasValue)
            {
                if (!(aggrLevelWithSerializedUnit.UnitQuantity.Value - this.BatchSerialization.ProductionQuantity < 0))
                {
                    result.Add(new BatchError()
                    {
                        IsCrirical = true,
                        UnitLevel = "AGGREGATION",
                        UnitValue = null,
                        Message = $"Production quantity is not enough for level {aggrLevelWithSerializedUnit.Unit} ({aggrLevelWithSerializedUnit.UnitQuantity.Value})."
                    });
                }
            }

            foreach (var aggregationSubLevel in this.BatchAggregations)
            {
                var aggregationParentLevel = this.BatchAggregations.FirstOrDefault(a => a.Item == aggregationSubLevel.Unit);
                if (aggregationParentLevel != null)
                {
                    if (aggregationParentLevel.ItemQuantity.HasValue)
                    {
                        if (!(aggregationParentLevel.UnitQuantity.Value * aggregationParentLevel.ItemQuantity.Value - aggregationSubLevel.UnitQuantity < aggregationParentLevel.ItemQuantity.Value))
                        {
                            result.Add(new BatchError()
                            {
                                IsCrirical = true,
                                UnitLevel = "AGGREGATION",
                                UnitValue = null,
                                Message = $"Unit quantity ({aggregationSubLevel.UnitQuantity}) in level {aggregationSubLevel.Unit} is not enough."
                            });
                        }
                    }
                    else
                    {
                        if (!(aggregationParentLevel.UnitQuantity.Value - aggregationSubLevel.UnitQuantity < 0))
                        {
                            result.Add(new BatchError()
                            {
                                IsCrirical = true,
                                UnitLevel = "AGGREGATION",
                                UnitValue = null,
                                Message = $"Unit quantity ({aggregationSubLevel.UnitQuantity}) in level {aggregationSubLevel.Unit} is not enough."
                            });
                        }
                    }

                }
            }

            return result;
        }


        private List<AzureEbrModels.BatchError> ValidateBatchAggregationExtend()
        {
            var result = new List<AzureEbrModels.BatchError>();

            foreach (var batchAggregation in this.BatchAggregations)
            {
                foreach (var batchAggregationExtend in batchAggregation.BatchAggregationExtends)
                {
                    var batchAggregationExtends = this.BatchAggregations
                        .Where(e => e.IDBatchAggregation == batchAggregationExtend.IDBatchAggregation)
                        .Select(b => b.BatchAggregationExtends)
                        .FirstOrDefault();

                    if (batchAggregationExtends != null && batchAggregationExtends.Count > 0)
                    {
                        result.AddRange(batchAggregationExtends
                        .Where(b => !CheckPropertyValueType(b.Type, b.StringValue))
                        .Select(b => new BatchError
                        {
                            IsCrirical = true,
                            UnitLevel = $"AGGREGATION", //TODO: Check if unit should be included// ({batchAggregation.Unit})",
                            UnitValue = null,
                            Message = $"Value {b.StringValue} cannot be converted to type {b.Type} ."
                        })
                        .ToList());

                        result.AddRange(batchAggregationExtends
                            .Where(be => (be.IsRequired && string.IsNullOrEmpty(be.StringValue)))
                            .Select(be => new BatchError()
                            {
                                IsCrirical = true,
                                UnitLevel = "AGGREGATION",
                                UnitValue = null,
                                Message = $"{be.Name} is required"
                            })
                            .ToList());

                        result.AddRange(batchAggregationExtends.Where(be => !string.IsNullOrEmpty(be.Regex)
                            && !string.IsNullOrEmpty(be.StringValue) && !new Regex(be.Regex).IsMatch(be.StringValue))
                            .Select(be => new BatchError
                            {
                                IsCrirical = true,
                                UnitLevel = "AGGREGATION",
                                UnitValue = null,
                                Message = $"Entered value for {be.Name} does not match format ({be.Regex})."
                            })
                            .ToList());
                    }
                }
            }

            return result;
        }

        private bool CheckPropertyValueType(ExtendPropertyEnum type, string value)
        {
            if (string.IsNullOrEmpty(value))
                return true;

            if (type == ExtendPropertyEnum.STRING)
            {
                return true;
            }
            else if (type == ExtendPropertyEnum.INT)
            {
                if (int.TryParse(value, out int ivalue))
                    return true;
                else
                    return false;
            }
            else if (type == ExtendPropertyEnum.DECIMAL)
            {
                if (decimal.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal dvalue))
                    return true;
                else
                    return false;
            }
            else if (type == ExtendPropertyEnum.DATETIME)
            {
                if (DateTime.TryParseExact(value, "yyyy-MM-ddTHH:mm:ss.fff", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dtvalue))
                    return true;
                else
                    return false;
            }
            else if (type == ExtendPropertyEnum.DATE)
            {
                if (DateTime.TryParseExact(value, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dtvalue))
                    return true;
                else
                    return false;
            }
            else if (type == ExtendPropertyEnum.BOOLEAN)
            {
                if (bool.TryParse(value, out bool bvalue))
                    return true;
                else
                    return false;
            }

            return false;
        }


        private List<BatchError> CheckLineSettings(string noSerializationType)
        {
            List<BatchError> result = new List<BatchError>();

            if (this.BatchProduct.SerializationType == noSerializationType && this.Line.HasSerialization)
            {
                result.Add(new BatchError
                {
                    IsCrirical = false,
                    UnitLevel = "LINE",
                    UnitValue = "LINE HAS SERIALIZATION",
                    Message = "PRODUCT HAS NO SERIALIZATION",

                });
            }
            if (this.BatchProduct.SerializationType != noSerializationType && !this.Line.HasSerialization)
            {
                result.Add(new BatchError
                {
                    IsCrirical = true,
                    UnitLevel = "LINE",
                    UnitValue = "LINE HAS NO SERIALIZATION",
                    Message = "PRODUCT HAS SERIALIZATION"
                });
            }

            if (!this.HasAggregation && this.Line.HasAggregation)
            {
                result.Add(new BatchError
                {
                    IsCrirical = false,
                    UnitLevel = "LINE",
                    UnitValue = "LINE HAS AGGREGATION",
                    Message = "BATCH HAS NO AGGREGATION"
                });
            }
            if (this.HasAggregation && !this.Line.HasAggregation)
            {
                result.Add(new BatchError
                {
                    IsCrirical = true,
                    UnitLevel = "LINE",
                    UnitValue = "LINE HAS NO AGGREGATION",
                    Message = "BATCH HAS AGGREGATION"
                });
            }

            return result;

        }

        private string SetAggregationSerialItemCode(bool isSGTIN, List<EbrLevels> ebrLevels, string level)
        {
            if (isSGTIN)
                return ebrLevels.FirstOrDefault(l => l.Name == level)?.GTIN;
            else
                return ebrLevels.FirstOrDefault(l => l.Name == level)?.SSCCPattern?.Substring(1);
        }


        public SgXmlEvent.SgXmlEvent ToSgXmlForExport(List<BatchSerializationSerial> serials,
                                                      List<BatchAggregation> aggregations,
                                                      List<ExtendProperty> extendProperties,
                                                      List<CustomerAggregationLevelView> aggregationLevels,
                                                      List<EbrLevels> ebrLevels)
        {
            var result = new SgXmlEvent.SgXmlEvent
            {
                CIP = this.BatchProduct.Code,
                LOT = this.BatchID,
                EXP = this.ExpiryDate,
                MNF = this.ManufacturingDate,
                SERIALSECTION = serials?
                        .Select(s => new SerialSection
                        {
                            AI91 = s.AI91,
                            AI92 = s.AI92,
                            SERIAL = s.SerialNumber,
                            State = s.State.ToString()
                        })
                        .ToList(),
                AGGREGATION = aggregations?
                        .Select(al => al.BatchAggregationSerials
                            .Select(s => new Serial
                            {
                                Code = s.SerialNumber,
                                ItemCount = s.BatchAggregationSerialItems.Count,
                                Type = s.IsSGTIN ? "SERIAL" : "SSCC",
                                Level = al.Unit,
                                ItemLevel = al.Item,
                                ItemType = al.ItemSerialType,
                                State = s.State.ToString(),
                                TypeCode = s.IsSGTIN ? al.GTIN : al.SSCCCopmanyPrefix,
                                Line = this.Line?.Code,
                                Timestamp = this.TimestampUpdated.HasValue ? this.TimestampUpdated.Value : this.TimestampCreated,
                                Items = s.BatchAggregationSerialItems
                                    .Select(i => new Serial.Item
                                    {
                                        Value = i.SerialNumber,
                                        SerialType = al.Item == "SERIALIZED UNIT" || i.IsSGTIN ? "SERIAL" : "SSCC",
                                        Code = SetAggregationSerialItemCode(i.IsSGTIN, ebrLevels, al.Item)
                                    }).ToList()

                            }))
                        .SelectMany(al => al)
                        .ToList(),
                Properties = new PropertyList()
                {
                    List = this.BatchExtends?
                        .Select(p => new Property
                        {
                            Name = p.Name,
                            Section = extendProperties.FirstOrDefault(e => e.IDExtendProperty == p.IDExtendProperty)?.Table.ToString(),
                            Value = p.StringValue
                        })
                        .ToList()
                }
            };

            if (this.BatchSerialization != null && this.BatchSerialization.BatchSerializationExtends != null)
            {
                result.Properties.List.AddRange(this.BatchSerialization.BatchSerializationExtends
                    .Select(p => new Property
                    {
                        Name = p.Name,
                        Section = extendProperties.FirstOrDefault(e => e.IDExtendProperty == p.IDExtendProperty)?.Table.ToString(),
                        Value = p.StringValue
                    })
                    .ToList());
            }

            if (this.BatchAggregations != null)
            {
                foreach (var aggregation in this.BatchAggregations)
                {
                    if (aggregation.BatchAggregationExtends != null)
                    {
                        result.Properties.List.AddRange(aggregation.BatchAggregationExtends
                            .Select(p => new Property
                            {
                                Name = p.Name,
                                Section = extendProperties.FirstOrDefault(e => e.IDExtendProperty == p.IDExtendProperty)?.Table.ToString(),
                                Value = p.StringValue,
                                Level = GetPropertyLevel(p.IDExtendProperty, extendProperties, aggregationLevels),
                            })
                            .ToList());

                    }
                }
            }

            result.Properties.List.Add(new Property { Section = TableValuesEnum.BATCH.ToString(), Name = "IDProduct", Value = this.IDProduct.Value.ToString() });
            result.Properties.List.Add(new Property { Section = TableValuesEnum.BATCH.ToString(), Name = "Line", Value = this.Line?.Code });
            result.Properties.List.Add(new Property { Section = TableValuesEnum.BATCH.ToString(), Name = "ExpiryDateZeroDay", Value = this.ExpiryDateZeroDay.ToString() });
            result.Properties.List.Add(new Property { Section = TableValuesEnum.BATCH.ToString(), Name = "TargetMarket", Value = this.TargetMarket?.ShortName });
            if (!string.IsNullOrEmpty(this.ProductionOrder))
                result.Properties.List.Add(new Property { Section = TableValuesEnum.BATCH.ToString(), Name = "ProductionOrder", Value = this.ProductionOrder });

            if (this.ProductionOrderDate.HasValue)
                result.Properties.List.Add(new Property { Section = TableValuesEnum.BATCH.ToString(), Name = "ProductionOrderDate", Value = this.ProductionOrderDate?.ToString("yyyy-MM-dd") });

            return result;
        }


        private string GetPropertyLevel(int idExtendProperty,
            List<ExtendProperty> extendProperties,
            List<CustomerAggregationLevelView> aggregationLevels)
        {
            var extendProperty = extendProperties.FirstOrDefault(e => e.IDExtendProperty == idExtendProperty);

            if (extendProperty != null && extendProperty.IDAggregationLevel.HasValue)
            {
                var aggregationLevel = aggregationLevels.FirstOrDefault(l => l.IDCustomerAggregationLevel == extendProperty.IDAggregationLevel.Value);
                if (aggregationLevel != null)
                    return aggregationLevel.Name;
                else
                    return null;
            }
            else
                return null;

        }

        private SgXmlEvent.SgXmlEvent LoadXmlFromBatch(List<BatchSerializationSerial> serials, List<BatchAggregation> aggregationLevels, List<EbrLevels> ebrLevels)
        {
            var result = new SgXmlEvent.SgXmlEvent
            {
                CIP = this.BatchProduct.Code,
                LOT = this.BatchID,
                EXP = this.ExpiryDate,
                MNF = this.ManufacturingDate,
                SERIALSECTION = serials
                    .Select(s => new SerialSection
                    {
                        AI91 = s.AI91,
                        AI92 = s.AI92,
                        SERIAL = s.SerialNumber,
                        State = s.State.ToString()
                    })
                    .ToList(),
                AGGREGATION = aggregationLevels
                    .Select(al => al.BatchAggregationSerials.Where(s => s.State != SerialStateEnum.ASSIGNED)
                        .Select(s => new Serial
                        {
                            Code = s.SerialNumber,
                            ItemCount = s.BatchAggregationSerialItems.Count,
                            Type = s.IsSGTIN ? "SERIAL" : "SSCC",
                            Level = al.Unit,
                            ItemLevel = al.Item,
                            ItemType = al.ItemSerialType,
                            State = s.State.ToString(),
                            TypeCode = s.IsSGTIN ? al.GTIN : al.SSCCCopmanyPrefix,
                            Line = this.Line?.Code,
                            Timestamp = this.TimestampUpdated.HasValue ? this.TimestampUpdated.Value : this.TimestampCreated,
                            Items = s.BatchAggregationSerialItems
                                    .Select(i => new Serial.Item
                                    {
                                        Value = i.SerialNumber,
                                        SerialType = i.IsSGTIN ? "SERIAL" : "SSCC",
                                        Code = SetAggregationSerialItemCode(i.IsSGTIN, ebrLevels, al.Item)
                                    }).ToList()

                        }))
                    .SelectMany(al => al)
                    .ToList()
            };

            return result;
        }

        public SgJsonEvent ToSgJsonEvent(List<BatchSerializationSerial> batchSerializationSerials,
                                                   List<BatchAggregation> batchAggregations)
        {
            var result = new SgJsonEvent
            {
                CIP = this.BatchProduct.Code,
                LOT = this.BatchID,
                EXP = this.ExpiryDate,
                MNF = this.ManufacturingDate,
                DAYFORMAT = this.ExpiryDateZeroDay ? "00" : null,
                SERIALSECTION = batchSerializationSerials
                        .Select(s => new SgXmlEvent.JsonSerialSection
                        {
                            SERIAL = s.SerialNumber,
                            State = s.State.ToString(),
                            AI91 = s.AI91,
                            AI92 = s.AI92
                        })
                        .ToList(),
                AGGREGATION = batchAggregations
                    .Select(a => a.BatchAggregationSerials
                        .Select(s => new SgXmlEvent.JsonSerial
                        {
                            Code = s.SerialNumber,
                            State = s.State.ToString(),
                            ItemCount = s.ItemCount,
                            ItemLevel = a.Item,
                            ItemType = a.ItemSerialType,
                            Type = a.SerialType,
                            Level = a.Unit,
                            Line = this.Line?.Code,
                            Items = s.BatchAggregationSerialItems
                                .Select(ai => ai.SerialNumber)
                                .ToList()
                        }).ToList()
                    )
                    .SelectMany(a => a)
                    .ToList(),
                LEVELS = GetSgXmlEventLevels()
            };

            return result;
        }

        private List<SgXmlEvent.Level> GetSgXmlEventLevels()
        {
            List<SgXmlEvent.Level> result = new List<Level>();

            if (this.HasSerialization)
            {
                result.Add(new Level
                {
                    Count = this.BatchSerialization.ProductionQuantity.Value,
                    UnitCapacity = 1,
                    Name = PackLevel.SERIALIZED_UNIT,
                    Type = PackLevel.LevelType.TRADE
                });
            }
            if (this.HasAggregation)
            {
                result.AddRange(this.BatchAggregations
                    .Select(a => new SgXmlEvent.Level
                    {
                        Count = a.UnitQuantity.Value,
                        UnitCapacity = a.ItemQuantity.Value,
                        Name = a.Unit,
                        Type = PackLevel.LevelType.LOGISTICS,
                        UnitName = a.Item
                    })
                    .ToList());
            }

            return result;
        }

        private void AddValdationProcess(SgXmlEventValidationProcess sgXmlEventValidationProcess)
        {
            if (this.ValidationProcess == null)
                this.ValidationProcess = new ObservableCollection<SgXmlEventValidationProcess>();

            sgXmlEventValidationProcess.ID = this.ValidationProcess.Count;

            ValidationProcess.Add(sgXmlEventValidationProcess);
        }

        private void UpdateValdationProcess(SgXmlEventValidationProcess sgXmlEventValidationProcess)
        {
            if (this.ValidationProcess == null)
                AddValdationProcess(sgXmlEventValidationProcess);
            else
            {
                var local = this.ValidationProcess.FirstOrDefault(p => p.ID == sgXmlEventValidationProcess.ID);
                if (local == null)
                    AddValdationProcess(sgXmlEventValidationProcess);
                else
                {
                    local.IsError = sgXmlEventValidationProcess.IsError;
                    local.IsWarning = sgXmlEventValidationProcess.IsWarning;
                    local.Level = sgXmlEventValidationProcess.Level;
                    local.Result = sgXmlEventValidationProcess.Result;
                }
            }
        }

        private void CheckValidationProcess(SgXmlEventValidationProcess sgXmlEventValidationProcess, List<BatchError> errors, DateTime startTime)
        {
            sgXmlEventValidationProcess.DurationSeconds = (DateTime.Now - startTime).TotalSeconds;
            if (errors.Count > 0)
            {
                if (errors.Any(e => e.IsCrirical == true))
                {
                    sgXmlEventValidationProcess.IsError = true;
                    sgXmlEventValidationProcess.Result = "ERROR";
                }
                else
                {
                    sgXmlEventValidationProcess.IsWarning = true;
                    sgXmlEventValidationProcess.Result = "WARNING";
                }
            }
            else
                sgXmlEventValidationProcess.Result = "SUCCESS";
        }



    }
}
