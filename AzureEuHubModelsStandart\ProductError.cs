﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("ProductError")]
    public partial class ProductError
    {
        [Key]
        public long IDProductError { get; set; }

        public int IDProductStatus { get; set; }

        [StringLength(100)]
        public string CorrelationID { get; set; }

        [StringLength(10)]
        public string Source { get; set; }

        [StringLength(2)]
        public string CountryCode { get; set; }

        [StringLength(10)]
        public string Code { get; set; }

        public string Description { get; set; }
    }
}
