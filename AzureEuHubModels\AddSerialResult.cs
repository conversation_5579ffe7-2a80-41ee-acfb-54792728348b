﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace AzureEuHubModels
{

    [Table("AddSerialResult")]
    public class AddSerialResult
    {
        [Key]
        public long IDAddSerialResult { get; set; }

        public int IDEvent { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        public string Message { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [StringLength(128)]
        public string UserCreated { get; set; }

    }
}
