﻿<PERSON><PERSON><PERSON> (12.03.2024)
Добавена колона Effective date в таблицата ProductExtend

alter table [dbo].[ProductExtend]
add EffectiveDate datetime null

Stoyan: test db executed 2024-03-18 11:31
________________________________________________________
<PERSON><PERSON><PERSON> (21.03.2024)
Добавени колони Effective date и Distribution state в таблицата BatchProduct

alter table [dbo].[BatchProduct]
add EffectiveDate datetime null


___________________________________________________________
Desislava <PERSON>d<PERSON> (04.09.2024)
Изтриване на таблица BatchTargetMarket

DROP TABLE [dbo].[BatchTargetMarket]
GO
_______________________________________________________________
Desislava Balkandzhieva (04.09.2024)
Добавяне на насройка SERIALS_DOWNLOAD_ENABLE

insert into Setting ([Key], [Value], TimestampCreated, UserCreated)
values ('SERIALS_DOWNLOAD_ENABLE', 'true', SYSDATETIME(), SYSTEM_USER)

_______________________________________________________________
Desislava Balkandzhieva (19.09.2024)

insert into [PackListErrorDescription] ([Code], [Description])
values 
('A29','Expiry Date Incorrect'),
('A36','Product Expired'),
('A37', 'Product On Recall'),
('A64 ', 'Pack Level Errors Exist'),
('A66', 'General Alert'),
('A67', 'Voluntary Decommission Possible'),
('A68', 'Batch Number Mismatch'),
('A69', 'Product Withdrawn'),
('A70', 'System Unavailable'),
('I2', 'Field out of range: {0}, Value: {1}, Reason: {2}'),
('I3', 'No Message'),
('I4', 'Unread Message'),
('O6', 'Unsolicited message received.'),
('O7', 'The certificate public key used is due to expire, client should switch to the new certificate.'),
('S14', 'Security Session Token Reset Failure'),
('S15', 'Security Session Token Reset Failure Reuse Of Last Password');
GO

_______________________________________________________________
Joro(20.05.2025)

ALTER TABLE ProductExtend
ADD AutoPublishTime DateTime null;

------ Stoyan (20.05.2025) Executed in TEST & PROD ---------------
__________________________________________________________________
Desislava Balkandzhieva (10.06.2025)
Add original ex date and is ex date zero date in batch 
__________________________________________________________________

ALTER TABLE Batch
ADD OriginalProductExpiryDate DATETIME NULL;

ALTER TABLE Batch
ADD IsOriginalProductExpiryDateZeroDay BIT NOT NULL DEFAULT 0;
_________________________________________________________________

------ Stoyan (11.06.2025) Executed in TEST & PROD ---------------


