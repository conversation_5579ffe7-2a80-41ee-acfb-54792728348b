﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("FileEventType")]
    public partial class FileEventType
    {
        [Key]
        [DisplayName("Event Type")]
        public int IDFileEventType { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("Name")]
        public string Name { get; set; }

        [StringLength(1024)]
        [DisplayName("Application Name")]
        public string AppName { get; set; }


        [InverseProperty("FileEventType")]
        public ICollection<FileWatcherConfig> FileWatcherConfigs { get; set; }
    }
}
