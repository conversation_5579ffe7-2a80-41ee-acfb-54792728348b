﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("HubPackStatus")]
    public class HubPackStatus
    {
        public HubPackStatus()
        {
            PackErrors = new HashSet<PackError>();
        }

        [Key]
        public long IDHubPackStatus { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? Timestamp { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        [InverseProperty("PackStatus")]
        public ICollection<PackError> PackErrors { get; set; }

    }



    public class HubPackStatusCustom
    {
        public HubPackStatus HubPackStatus { get; set; }
        //public List<CustomModels.PackListErrorSummary> PackListErrorSummary { get; set; }

        public HubPackStatusCustom()
        {
            HubPackStatus = new HubPackStatus();
            //PackListErrorSummary = new List<CustomModels.PackListErrorSummary>();
        }
    }
}
