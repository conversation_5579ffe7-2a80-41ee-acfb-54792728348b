﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModelsStandart
{
    [Table("ProductHubEvent")]
    public class ProductHubEvent
    {
       
        [Key]
        public int IDProductHubEvent { get; set; }

        [Column("IDproduct")]
        public int IDProduct { get; set; }

        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [StringLength(50)]
        public string EventName { get; set; }

        public int? VersionNumber { get; set; }

        [StringLength(255)]
        public string InfoMessage { get; set; }

        public bool? IsConfirmed { get; set; }

    }


    [Table("ProductHubEventMarket")]
    public class ProductHubEventMarket
    {
        [Key]
        public int IDProductHubEventMarket { get; set; }

        [Column("IDProductHubEvent")]
        public int IDProductHubEvent { get; set; }

        [StringLength(2)]
        public string Market { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

    }


    [Table("EventConfirm")]
    public class EventConfirm
    {
        [Key]
        public int IDEventConfirm { get; set; }

        public int IDProductHubEvent { get; set; }

        [StringLength(128)]
        public string LgnName { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampCreated { get; set; }

        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampConfirm { get; set; }

        public bool? UserConfirm { get; set; }

        [StringLength(512)]
        public string UserNote { get; set; }

        public bool IsEmailSent { get; set; }

    }

}
