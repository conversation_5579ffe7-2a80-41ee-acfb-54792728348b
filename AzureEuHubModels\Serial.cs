﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("Serial")]
    public class Serial
    {
        [Key]
        public long IDSerials { get; set; }

        public int IDEvent { get; set; }

        [Required]
        [StringLength(20)]
        public string SerialNumber { get; set; }

        public short? IDPackState { get; set; }

        [InverseProperty("Serials")]
        public Event Event { get; set; }

        [InverseProperty("Serials")]
        public PackState PackState { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((Serial)obj).IDSerials == this.IDSerials)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }

    }
}
