﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("BatchRecallMarket")]
    public class BatchRecallMarket
    {
        [Key]
        public int IDBatchRecallMarket { get; set; }

        [Required]
        [StringLength(100)]
        public string CorrelationID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime Timestamp { get; set; }

        [StringLength(2)]
        public string Market { get; set; }

        public bool Recalled { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((BatchRecallMarket)obj).IDBatchRecallMarket == this.IDBatchRecallMarket)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }
    }
}
