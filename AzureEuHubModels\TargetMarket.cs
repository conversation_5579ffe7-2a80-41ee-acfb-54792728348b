﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEuHubModels
{
    [Table("TargetMarket")]
    public class TargetMarket
    {
        public TargetMarket()
        {
            ProductTargetMarkets = new HashSet<ProductTargetMarket>();
            Markets = new HashSet<Market>();
        }

        [Key]
        public int IDTargetMarket { get; set; }

        [Required]
        public int IDCMDTargetMarkert { get; set; }

        [Required]
        [StringLength(128)]
        public string Name { get; set; }

        [StringLength(4)]
        public string ShortName { get; set; }

        public int? GS1RNCode { get; set; }

        [StringLength(50)]
        public string SerializationType { get; set; }

        [StringLength(2)]
        public string SrcSystem { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("TargetMarket")]
        public ICollection<ProductTargetMarket> ProductTargetMarkets { get; set; }

        [InverseProperty("TargetMarket")]
        public ICollection<Market> Markets { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((TargetMarket)obj).IDTargetMarket == this.IDTargetMarket)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }
    }
}
